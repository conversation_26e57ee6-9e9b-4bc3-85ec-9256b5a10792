import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useCreateFolderActions, useParentFolderId } from "~/store/createFolderStore";
import { useDialogActions } from "~/store/dialogStore";
import { createErrorHand<PERSON> } from "~/helpers/errorHandling.helper";
import { useEncryption } from "~/hooks/useEncryption";
import { createFolder as createFolderAPI } from "~/services/gateways/report.gateway";
import { EncryptedPayload } from "~/types/global";

export interface CreateFolderRequest {
  folderName: string;
  selectedReports?: string[];
  parentFolderId?: string | null;
}

export interface CreateFolderResponse {
  folder_id: string;
  folder_name: string;
  created_at: string;
  reports_count: number;
}

interface CreateFolderAPIPayload {
  folder_name: EncryptedPayload;
  parent_folder_id: string | null;
  user_reports_id_list: string[];
  hmac_folder_name: string[];
}

export const useFolderCRUD = () => {
  const queryClient = useQueryClient();
  const { closeDialog } = useDialogActions();
  const { encryptData, encryptNgrams } = useEncryption();
  const {
    clearFolderData
  } = useCreateFolderActions();
  const parentFolderId = useParentFolderId();

  const createFolderMutation = useMutation({
    mutationFn: async (payload: CreateFolderRequest) => {
      try {
        const folderName = payload.folderName
        console.log("[useFolderCRUD] createFolderMutation folderName", folderName);
        const encryptedFolderName = await encryptData(folderName);
        if (!encryptedFolderName.success || !encryptedFolderName.data) {
          throw new Error("Erro ao criptografar nome da pasta");
        }
        // HMAC
        const hmacResult = await encryptNgrams({
          folder_name: folderName,
        });
        const apiPayload: CreateFolderAPIPayload = {
          folder_name: encryptedFolderName.data,
          parent_folder_id: parentFolderId,
          user_reports_id_list: payload.selectedReports || [],
          hmac_folder_name: hmacResult.folder_name || [],
        };

        console.log("[useFolderCRUD] createFolderMutation payload", apiPayload);
        return createFolderAPI(apiPayload);
      } catch (error) {
        console.error("Error creating folder:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      await queryClient.invalidateQueries({
        queryKey: ["reports"],
        exact: true,
      });

      clearFolderData();
      closeDialog();

      toast.success(`Pasta criada com sucesso!`, {
        description: `Pasta criada com ${data?.reports_count || 0} relatórios`,
      });
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar criar a pasta",
        "Erro ao criar pasta"
      )(error);
    },
  });

  return {
    createFolderMutation,
  };
};
