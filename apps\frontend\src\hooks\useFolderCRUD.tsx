import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useCreateFolderActions } from "~/store/createFolderStore";
import { useDialogActions } from "~/store/dialogStore";
import { createErrorHandler } from "~/helpers/errorHandling.helper";

export interface CreateFolderRequest {
  folderName: string;
  selectedReports?: string[];
}

export interface CreateFolderResponse {
  folder_id: string;
  folder_name: string;
  created_at: string;
  reports_count: number;
}

// Placeholder API function - to be implemented when backend is ready
const createFolder = async (payload: CreateFolderRequest): Promise<CreateFolderResponse> => {
  // TODO: Replace with actual API call when backend endpoint is implemented
  // For now, simulate API call
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (payload.folderName.trim().length === 0) {
        reject(new Error("Nome da pasta é obrigatório"));
        return;
      }
      
      // Simulate successful creation
      resolve({
        folder_id: `folder_${Date.now()}`,
        folder_name: payload.folderName,
        created_at: new Date().toISOString(),
        reports_count: payload.selectedReports?.length || 0,
      });
    }, 1000); // Simulate network delay
  });
};

export const useFolderCRUD = () => {
  const queryClient = useQueryClient();
  const { closeDialog } = useDialogActions();
  const { 
    setIsCreating, 
    setError, 
    clearFolderData, 
    resetStore 
  } = useCreateFolderActions();

  const createFolderMutation = useMutation({
    mutationFn: createFolder,
    onMutate: () => {
      setIsCreating(true);
      setError(null);
    },
    onSuccess: async (data) => {
      // Invalidate reports list to refresh the UI
      await queryClient.invalidateQueries({
        queryKey: ["reports"],
      });
      
      // Clear folder data and close dialog
      clearFolderData();
      closeDialog();
      
      toast.success(`Pasta "${data.folder_name}" criada com sucesso!`, {
        description: `${data.reports_count} relatórios adicionados à pasta`,
      });
    },
    onError: (error: Error) => {
      setError(error.message);
      createErrorHandler(
        "Ocorreu um erro ao tentar criar a pasta", 
        "Erro ao criar pasta"
      )(error);
    },
    onSettled: () => {
      setIsCreating(false);
    },
  });

  const handleCreateFolder = async (folderName: string, selectedReports: string[] = []) => {
    if (!folderName.trim()) {
      setError("Nome da pasta é obrigatório");
      return;
    }

    try {
      await createFolderMutation.mutateAsync({
        folderName: folderName.trim(),
        selectedReports,
      });
    } catch (error) {
      // Error is already handled in onError
      console.error("Error creating folder:", error);
    }
  };

  const cancelFolderCreation = () => {
    if (createFolderMutation.isPending) {
      // If mutation is pending, we can't cancel it, but we can reset the UI
      setError("Operação cancelada pelo usuário");
    }
    resetStore();
    closeDialog();
  };

  return {
    createFolderMutation,
    handleCreateFolder,
    cancelFolderCreation,
    isCreating: createFolderMutation.isPending,
    error: createFolderMutation.error?.message || null,
  };
};
