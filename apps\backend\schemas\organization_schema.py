from datetime import datetime
from pydantic import BaseModel, <PERSON>
from typing import Optional
from uuid import UUID

class OrganizationBase(BaseModel):
    """Base schema for organization data"""
    name: str = Field(..., description="Name of the organization")
    image_logo: Optional[str] = Field(None, description="Optional URL or path to the organization's logo image")
    api_key: Optional[str] = Field(..., description="API key associated with the organization")


class OrganizationCreate(OrganizationBase):
    """Schema for creating a new organization"""
    pass


class OrganizationUpdate(BaseModel):
    """Schema for updating an existing organization"""
    name: Optional[str] = Field(None, description="Name of the organization")
    image_logo: Optional[str] = Field(None, description="URL or path to the organization's logo image")
    api_key: Optional[str] = Field(None, description="API key associated with the organization")


class OrganizationOut(OrganizationBase):
    """Schema for organization response"""
    organization_id: UUID = Field(..., description="Unique identifier for the organization")
    created_at: datetime = Field(..., description="Timestamp when the organization was created")

    class Config:
        from_attributes = True  # Allows ORM model to be converted to this schema


class OrganizationInDB(OrganizationOut):
    """Schema for organization as stored in database"""
    pass
