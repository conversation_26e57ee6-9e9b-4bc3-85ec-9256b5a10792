import {Image, Rect, Svg, Text, View} from "./pdf-components";
import React from "react";
import {PdfStyles} from "./styles";
import {GRAFISMO_SRC, LOGO_SRC} from "../config/assets";
import {ReportIconKeys, ReportsIconsMap} from "../config/report";
import {IReportsPdfHeaderProps} from "../global";

export const ReportsPdfHeader = ({reportType, searchValue, title}: IReportsPdfHeaderProps) =>
  <View style={PdfStyles.header} fixed>
    <View style={PdfStyles.logoContainer}>
      <Image src={LOGO_SRC} style={PdfStyles.logo}/>
    </View>

    <View style={PdfStyles.headerContent} className={"headerContent"}>
      <Text style={PdfStyles.title} className={"title"}>{title.toUpperCase()}</Text>
      <View style={PdfStyles.reportTypeContainer} className={"reportTypeContainer"}>
        {ReportIconKeys.has(reportType) ? (
          <Image className={"reportIcon"} src={ReportsIconsMap.get(reportType) as string} style={PdfStyles.searchIcon}/>
        ) : (
          <Svg className={"reportIcon"} width={8} height={8} viewBox="0 0 8 8" style={PdfStyles.searchIcon}>
            <Rect width="8" height="8" fill="#FE473C"/>
          </Svg>
        )}
        <Text className={"searchValue"} style={PdfStyles.searchValue}>{searchValue}</Text>
      </View>
    </View>

    <Image className={"spiralImage"} src={GRAFISMO_SRC} style={PdfStyles.spiralImage}/>
  </View>

export const wrapHeaderWithInlineCSS = (html: string): string => {
  return `
    <style>${
    Object.entries(PdfStyles)
      .map(([key, value]) => `.${key} { ${Object.entries(value).map(([k, v]) => `${k}: ${v};`)
        .join(' ')} }`).join('\n')}
    }</style>
    ${html}
  `;
}