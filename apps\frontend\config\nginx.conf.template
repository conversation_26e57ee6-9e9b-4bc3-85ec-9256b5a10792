worker_processes 1;

events {
  worker_connections 1024;
}

http {
  log_format json_analytics escape=json '{'
  '"msec": "$msec", '
  '"connection": "$connection", '
  '"connection_requests": "$connection_requests", '
  '"pid": "$pid", '
  '"request_id": "$request_id", '
  '"request_length": "$request_length", '
  '"remote_addr": "$remote_addr", '
  '"remote_user": "$remote_user", '
  '"remote_port": "$remote_port", '
  '"time_local": "$time_local", '
  '"time_iso8601": "$time_iso8601", '
  '"request": "$request", '
  '"request_uri": "$request_uri", '
  '"args": "$args", '
  '"status": "$status", '
  '"body_bytes_sent": "$body_bytes_sent", '
  '"bytes_sent": "$bytes_sent", '
  '"http_referer": "$http_referer", '
  '"http_user_agent": "$http_user_agent", '
  '"http_x_forwarded_for": "$http_x_forwarded_for", '
  '"http_host": "$http_host", '
  '"server_name": "$server_name", '
  '"request_time": "$request_time", '
  '"upstream": "$upstream_addr", '
  '"upstream_connect_time": "$upstream_connect_time", '
  '"upstream_header_time": "$upstream_header_time", '
  '"upstream_response_time": "$upstream_response_time", '
  '"upstream_response_length": "$upstream_response_length", '
  '"upstream_cache_status": "$upstream_cache_status", '
  '"ssl_protocol": "$ssl_protocol", '
  '"ssl_cipher": "$ssl_cipher", '
  '"scheme": "$scheme", '
  '"request_method": "$request_method", '
  '"server_protocol": "$server_protocol", '
  '"pipe": "$pipe", '
  '"gzip_ratio": "$gzip_ratio"'
  '}';

  access_log /var/log/nginx/json_access.log json_analytics;

server {
    listen 80;
    server_name localhost;

    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name localhost;

    # SSL configuration (cert, protocols, etc.)
    ssl_certificate /etc/nginx/nginx-certs/mysite.crt;
    ssl_certificate_key /etc/nginx/nginx-certs/mysite.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Serve static assets
    root /usr/share/nginx/html;
    index index.html index.htm;
    include /etc/nginx/mime.types;

    gzip on;
    gzip_min_length 1000;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    error_page 404 /404.html;
    location = /404.html {
      root /usr/share/nginx/html;
      internal;
    }

    # Expose stub_status for health/metrics
    location = /stub_status {
      stub_status on;
    }

    # Serve your frontend application (SPA)
    location / {
      try_files $uri $uri/ /index.html;
    }

    # Proxy API calls
    location /pdf/ {
      client_max_body_size 100M;
      proxy_pass $NGINX_PDF_SERVICE_URL;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      # Extended timeout settings for large PDF generation (up to 20 minutes)
      proxy_connect_timeout 600s;
      proxy_send_timeout 600s;
      proxy_read_timeout 600s;

      # Disable buffering for streaming responses
      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /reports/ {
      client_max_body_size 100M;  # Add this line
      proxy_pass $NGINX_REPORTS_API_URL;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      # Extended timeout settings for large PDF generation (up to 20 minutes)
      proxy_connect_timeout 1200s;
      proxy_send_timeout 1200s;
      proxy_read_timeout 1200s;
    }

    location /reports/ws/ {
      proxy_pass $NGINX_REPORTS_API_URL;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }
  }
}
