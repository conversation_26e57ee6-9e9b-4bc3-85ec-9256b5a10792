import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type CreateDialogTab = "report" | "folder";

interface FolderCreationState {
  folderName: string;
  selectedReports: string[];
  isCreating: boolean;
  error: string | null;
}

interface TabState {
  activeTab: CreateDialogTab;
}

interface CreateFolderActions {
  // Folder actions
  setFolderName: (name: string) => void;
  setSelectedReports: (reportIds: string[]) => void;
  addSelectedReport: (reportId: string) => void;
  removeSelectedReport: (reportId: string) => void;
  clearSelectedReports: () => void;
  setIsCreating: (isCreating: boolean) => void;
  setError: (error: string | null) => void;
  clearFolderData: () => void;
  // Tab actions
  setActiveTab: (tab: CreateDialogTab) => void;
  // Combined actions
  resetStore: () => void;
}

interface CreateFolderState extends FolderCreationState, TabState {
  actions: CreateFolderActions;
}

const initialFolderState: FolderCreationState = {
  folderName: "",
  selectedReports: [],
  isCreating: false,
  error: null,
};

const initialTabState: TabState = {
  activeTab: "report",
};

const useCreateFolderStore = create<CreateFolderState>()(
  devtools(
    (set, get) => ({
      ...initialFolderState,
      ...initialTabState,
      actions: {
        setFolderName: (name: string) => 
          set({ folderName: name, error: null }, false, "setFolderName"),
        
        setSelectedReports: (reportIds: string[]) => 
          set({ selectedReports: reportIds }, false, "setSelectedReports"),
        
        addSelectedReport: (reportId: string) => 
          set((state) => ({
            selectedReports: state.selectedReports.includes(reportId)
              ? state.selectedReports
              : [...state.selectedReports, reportId]
          }), false, "addSelectedReport"),
        
        removeSelectedReport: (reportId: string) => 
          set((state) => ({
            selectedReports: state.selectedReports.filter(id => id !== reportId)
          }), false, "removeSelectedReport"),
        
        clearSelectedReports: () => 
          set({ selectedReports: [] }, false, "clearSelectedReports"),
        
        setIsCreating: (isCreating: boolean) => 
          set({ isCreating }, false, "setIsCreating"),
        
        setError: (error: string | null) => 
          set({ error }, false, "setError"),
        
        clearFolderData: () => 
          set({ 
            folderName: "", 
            selectedReports: [], 
            error: null 
          }, false, "clearFolderData"),
        
        setActiveTab: (tab: CreateDialogTab) => 
          set({ activeTab: tab }, false, "setActiveTab"),
        
        resetStore: () => 
          set({ 
            ...initialFolderState, 
            ...initialTabState 
          }, false, "resetStore"),
      },
    }),
    {
      name: "create-folder-store",
    }
  )
);

export const useFolderName = () => 
  useCreateFolderStore((state) => state.folderName);

export const useSelectedReports = () => 
  useCreateFolderStore((state) => state.selectedReports);

export const useIsCreatingFolder = () => 
  useCreateFolderStore((state) => state.isCreating);

export const useFolderError = () => 
  useCreateFolderStore((state) => state.error);

export const useActiveTab = () => 
  useCreateFolderStore((state) => state.activeTab);

export const useCreateFolderActions = () => 
  useCreateFolderStore((state) => state.actions);

export const useCanCreateFolder = () => 
  useCreateFolderStore((state) => 
    state.folderName.trim().length > 0 && !state.isCreating
  );

export const useHasSelectedReports = () => 
  useCreateFolderStore((state) => state.selectedReports.length > 0);