import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import insert, select, update
from datetime import datetime, timedelta

from core.constants import Fields, StatusOrganization, OrganizationUsersParameters

from models.organization_users_model import OrganizationUsers

from services.base_service import BaseService

from models.enums import UserStatus
from exceptions.business_exceptions import OrganizationUsersDatabaseOperationError

logger = logging.getLogger(__name__)


class OrganizationUsersService(BaseService):


    def __init__(self, db: AsyncSession, user_id: str) -> None:
         super().__init__(db)
         self.user_id=user_id
         self.organization_id=None
         logger.debug("[OrganizationUsersService] Initialized with user_id: %s", user_id)


    async def insert_organization_users_into_local_db(self, organization_id: str):      
            
            logger.info(
                "[OrganizationUsers] Starting organization user insertion - Organization ID: %s, User ID: %s",
                organization_id,
                self.user_id
            )

            save_data = {
                Fields.organization_id: organization_id,
                Fields.user_id: self.user_id,
                Fields.status: StatusOrganization.ativo,
                Fields.joined_at: datetime.now(),
                Fields.validate_until: datetime.now() + timedelta(days=OrganizationUsersParameters.validate_until_days)
            }

            logger.debug("[OrganizationUsers] Prepared save data: %s", save_data)

            try:
                query = (
                    insert(OrganizationUsers)
                    .values(**save_data)
                    .returning(OrganizationUsers.organization_users_id)  # Only return the ID
                )
                logger.debug("[OrganizationUsers] Executing insert query with data: %s", save_data)

                result = await self.db.execute(query)
                await self.db.commit()
            
                organization_users_id = result.scalar_one()
                logger.info(
                    "[OrganizationUsers] Successfully inserted organization user - ID: %s",
                    organization_users_id
                )

                return organization_users_id

            except Exception as e:
                await self.db.rollback()
                logger.error(
                    "[OrganizationUsers] Failed to insert organization user - Organization ID: %s, User ID: %s, Error: %s",
                    organization_id,
                    self.user_id,
                    str(e),
                    exc_info=True
                )
                raise OrganizationUsersDatabaseOperationError(str(e))
            

    async def get_organization_user(self) -> dict:
        
        """
        Get the data for a user from organization_users table.
            
        Returns:
            str: The organization_id for the active user
            
        Raises:
            HTTPException: If user is not found or has no active organization
        """
        logger.info("[OrganizationUsersService] Entered get_organization_user for user_id: %s", self.user_id)
        try:
            logger.info(f"UserStatus.ATIVO = {UserStatus.ATIVO!r}")
            logger.info(f"type(UserStatus.ATIVO) = {type(UserStatus.ATIVO)}")


            query = (
                select(OrganizationUsers)
                .where(
                    OrganizationUsers.user_id == self.user_id,
                    OrganizationUsers.status == UserStatus.ATIVO
                )
            )
            
            logger.debug("[OrganizationUsersService] Executing organization query for user: %s", self.user_id)
            result = await self.db.execute(query)
            org_user = result.scalars().first()          

            if org_user:
                logger.info("[OrganizationUsersService] Found organization_id: %s for user: %s", org_user  , self.user_id)
                self.organization_id=org_user.organization_id
                logger.info("[OrganizationUsersService] Found organization_id: %s for user: %s", self.organization_id  , self.user_id)
            else:
                logger.warning("[OrganizationUsersService] No active organization user found for user_id: %s", self.user_id)
            return org_user  
            
        except Exception as e:
            logger.error(
                "[OrganizationUsersService] Failed to get organization_id for user %s. Error: %s",
                self.user_id,
                str(e),
                exc_info=True
            )
            raise OrganizationUsersDatabaseOperationError(str(e))
        

    async def update_organization_user_status_to_inativo(self):
        """
        Update the status of an organization user to inactive.
        """
        logger.info("[OrganizationUsersService] Updating organization user status to inactive for organization_id: %s", self.organization_id)
        try:
            query = (
                update(OrganizationUsers)
                .where(OrganizationUsers.user_id == self.user_id)
                .where(OrganizationUsers.organization_id == self.organization_id)
                .values(status=UserStatus.INATIVO, exited_at=datetime.now())
            )
            logger.debug("[OrganizationUsersService] Executing update query for organization_id: %s", self.organization_id)
            result = await self.db.execute(query)
            await self.db.commit()
            logger.info("[OrganizationUsersService] Successfully updated organization user status to inactive for organization_id: %s", self.organization_id)
            return result
        except Exception as e:
            await self.db.rollback()    
            logger.error("[OrganizationUsersService] Failed to update organization user status to inactive for organization_id: %s. Error: %s", self.organization_id, str(e), exc_info=True)
            raise OrganizationUsersDatabaseOperationError(str(e))
        

    async def leave_organization(self):
        """
        Leave an organization.
        """
        logger.info("[OrganizationUsersService] Leaving organization for organization_id: %s", self.organization_id)
        try:
            query = (
                update(OrganizationUsers)
                .where(OrganizationUsers.organization_id == self.organization_id,
                       OrganizationUsers.user_id == self.user_id)
                .values(status=UserStatus.INATIVO, exited_at=datetime.now(), validate_until=datetime.now())
            )
            logger.debug("[OrganizationUsersService] Executing update query for organization_id: %s", self.organization_id)
            result = await self.db.execute(query)
            await self.db.commit()
            logger.info("[OrganizationUsersService] Successfully left organization for organization_id: %s", self.organization_id)
            return result
        except Exception as e:
            await self.db.rollback()
            logger.error("[OrganizationUsersService] Failed to leave organization for organization_id: %s. Error: %s", self.organization_id, str(e), exc_info=True)
            raise OrganizationUsersDatabaseOperationError(str(e))