import React from 'react';
import { View, Text, StyleSheet, Image } from './pdf-components';
import { ReportMetadata } from '../global';
import { getAgeFromBirthDate, getInitials, isReportSubjectCompany } from '../helpers';
import { REPORT_CONSTANTS } from '../config/constants';
import { MULTIPLE_USERS_ICON, MULTIPLE_BUILDINGS_ICON } from '../config/assets';

interface PrintProfileHeaderProps {
  metadata: ReportMetadata;
  report_type: string;
  searchValue: string;
  profile_image?: string;
}

export const PrintProfileHeader: React.FC<PrintProfileHeaderProps> = ({ metadata, report_type, searchValue, profile_image }) => {
  const profileName = metadata[REPORT_CONSTANTS.new_report.subject_name] as string;
  const initials = getInitials(profileName as string);
  const isMultipleSubjects = profileName === REPORT_CONSTANTS["multiplos_registros_encontrados"];
  const ageFromMeta = metadata[REPORT_CONSTANTS.new_report.subject_age] as string | number | undefined;
  const idade = typeof ageFromMeta === 'number' ? ageFromMeta : getAgeFromBirthDate(metadata[REPORT_CONSTANTS.new_report.subject_age] as string);
  const data_fundacao = new Date(ageFromMeta ? ageFromMeta as string : '').toLocaleDateString('pt-BR');
  const sexo = metadata[REPORT_CONSTANTS.new_report.subject_sex] as string;
  const nomeMae = metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string;
  const companyCount = metadata[REPORT_CONSTANTS.new_report.subject_company_count];
  const personCount = metadata[REPORT_CONSTANTS.new_report.subject_person_count];
  const reportSearchArgs = metadata[REPORT_CONSTANTS.new_report.report_search_args];

  const isCompany = isReportSubjectCompany(
    report_type,
    ageFromMeta,
    sexo,
    nomeMae
  );

  const communProps = [
    { label: 'PESSOAS ENCONTRADAS', value: personCount },
    { label: 'EMPRESAS ENCONTRADAS', value: companyCount },
  ];

  const ageLabel = isCompany ? 'DATA DE FUNDAÇÃO' : 'IDADE';
  const ageValue = isCompany ? data_fundacao : idade;

  const personProps = [
    { label: ageLabel, value: ageValue },
    { label: 'SEXO', value: sexo },
    { label: 'NOME DA MÃE', value: nomeMae },
  ];

  const companyProps = [
    { label: 'STATUS NA RECEITA', value: 'ATIVO' }, // TODO - mockado
    { label: ageLabel, value: ageValue },
  ];

  const isValidValue = (val: any) => val !== undefined && val !== null && val !== '' && val !== 0;

  const getReportProps = () => {
    const props = [{ label: 'NOME', value: profileName }, ...communProps];

    if (isCompany) {
      props.push(...companyProps);
    } else {
      props.push(...personProps);
    }

    return props.filter(prop => isValidValue(prop.value));
  };

  const dataItems = getReportProps();
  const midpoint = Math.ceil(dataItems.length / 2);
  const firstColumn = dataItems.slice(0, midpoint);
  const secondColumn = dataItems.slice(midpoint);

  const getSearchArguments = () => {
    if (!reportSearchArgs || typeof reportSearchArgs !== 'object') {
      return [{ key: report_type.toUpperCase(), value: searchValue }];
    }

    const searchArgsRecord = reportSearchArgs as Record<string, unknown>;
    return Object.entries(searchArgsRecord)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => ({
        key: key.toUpperCase(),
        value: String(value)
      }));
  };

  const renderDataColumn = (columnItems: { label: string; value: string | number }[]) => (
    <View style={styles.dataColumn}>
      {columnItems.map((item, idx) => (
        <View key={idx} style={styles.dataRow}>
          <Text style={styles.dataLabel}>{item.label}</Text>
          <Text style={styles.dataValue}>{item.value}</Text>
        </View>
      ))}
    </View>
  );

  return (
    <View style={styles.headerContainer}>
      <View style={styles.topRow}>
        <View style={styles.profileHeaderSection}>
          <Text style={styles.sectionHeader}>PERFIL</Text>
          <View style={styles.photoContainer}>
            {!isMultipleSubjects ? (
              <View style={styles.avatarBox}>
                {profile_image ? (
                  <View style={styles.imageBorder}>
                    <Image src={profile_image} style={styles.profileImage} />
                  </View>
                ) : (
                  <Text style={styles.avatarFallback}>{initials}</Text>
                )}
              </View>
            ) : (
              <View style={styles.avatarBox}>
                <View style={styles.multipleIconContainer}>
                  {(personCount ? MULTIPLE_USERS_ICON : MULTIPLE_BUILDINGS_ICON) ? (
                    <Image
                      src={personCount ? MULTIPLE_USERS_ICON : MULTIPLE_BUILDINGS_ICON}
                      style={styles.multipleIcon}
                    />
                  ) : (
                    <Text style={styles.multipleIconFallback}>
                      {personCount ? 'P' : 'E'}
                    </Text>
                  )}
                </View>
              </View>
            )}
          </View>
        </View>

        <View style={styles.entradasSection}>
          <Text style={styles.sectionHeader}>ENTRADAS</Text>
          {getSearchArguments().map((entry, index) => (
            <View key={index} style={styles.entryContainer}>
              <Text style={styles.dataLabel}>{entry.key}</Text>
              <View style={styles.entryBox}>
                <Text style={styles.dataValue}>{entry.value}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.dataListContainer}>
        {renderDataColumn(firstColumn)}
        {renderDataColumn(secondColumn)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    width: "100%",
    flexDirection: "column",
  },
  topRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  profileHeaderSection: {
    width: "48%",
    paddingRight: 16,
  },
  entradasSection: {
    width: "48%",
  },
  dataListContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dataColumn: {
    width: "48%",
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  photoContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  avatarBox: {
    width: 144,
    height: 144,
    borderRadius: 72,
    backgroundColor: "#E5E7EB",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  avatarFallback: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#6B7280",
    textAlign: "center",
  },
  dataRow: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottom: "1px solid #E5E7EB",
  },
  dataLabel: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 4,
    letterSpacing: 0.5,
    textTransform: "uppercase",
  },
  dataValue: {
    fontSize: 10,
    fontWeight: "normal",
  },
  entryContainer: {
    padding: 8,
    backgroundColor: "#F9F9FA",
    borderRadius: 4,
  },
  entryBox: {
    borderTopWidth: 1,
    borderColor: "#E5E7EB",
    paddingVertical: 4,
  },
  imageBorder: {
    width: 140,
    height: 140,
    borderRadius: 70,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  profileImage: {
    width: 136,
    height: 136,
    borderRadius: 68,
  },
  multipleIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#F3F4F6",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  multipleIcon: {
    width: 64,
    height: 50,
  },
  multipleIconFallback: {
    fontSize: 36,
    fontWeight: "bold",
    color: "#6B7280",
    textAlign: "center",
  },
});
