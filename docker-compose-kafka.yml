services:
  kafka:
    image: my-kafka-image
    build:
      context: apps/kafka
      dockerfile: Dockerfile
    container_name: ${KAFKA_CONTAINER_NAME:-kafka}
    networks:
      - mystack-net
    ports:
      - "${KAFKA_EXTERNAL_PORT:-9093}:9093"
    environment:
      - KAFKA_CFG_PROCESS_ROLES=broker,controller
      - KAFKA_CFG_NODE_ID=1
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9094
      - KAFKA_CFG_LISTENERS=INTERNAL://:9092,EXTERNAL://:9093,CONTROLLER://:9094
      - <PERSON><PERSON>KA_CFG_ADVERTISED_LISTENERS=INTERNAL://kafka:9092,EXTERNAL://$KAFKA_EXTERNAL_URI:$KAFKA_EXTERNAL_PORT
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT,CONTROLLER:PLAINTEXT
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=INTERNAL
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_KRAFT_MODE=true
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
      - BITNAMI_DEBUG=true
      - KAFKA_KRAFT_CLUSTER_ID=2EC08F13-BCF6-4B02-BC93-7712289B4FA1
      # - KAFKA_LOG4J_OPTS=-Dlog4j.configurationFile=/opt/bitnami/kafka/config/log4j2.xml

    volumes:
      - kafka_data:/bitnami/kafka
    deploy:
      resources:
        limits:
          cpus: "1.5"
          memory: "2G"
        reservations:
          cpus: "1.0"
          memory: "1G"
    restart: unless-stopped

volumes:
  kafka_data:
