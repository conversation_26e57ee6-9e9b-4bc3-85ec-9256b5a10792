import { Loading, Text, Button } from "@snap/design-system";
import { X, Mail, CheckIcon, MailWarning, MailX, Building2, AlertTriangle } from "lucide-react";
import { useEffect } from "react";
import { toast } from "sonner";
import { Column, DataTable } from "~/components/Table";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useDialogActions, useIsDialogIsOpen } from "~/store/dialogStore";
import { useUserInviteList, useUserInviteListActions } from "~/store/userInviteListStore";
import { useUserData } from "~/store/userStore";
import { UserInviteResponse } from "~/types/global";
import { USER_CONSTANTS } from "~/helpers/constants";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

interface InviteListDialogContentProps { }

interface OrganizationChangeConfirmationProps {
  currentOrganization: string;
  newOrganization: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

interface OrganizationChangeConfirmationFooterProps {
  onConfirm?: () => void;
  onCancel?: () => void;
}

const columnProps = {
  invite_id: USER_CONSTANTS.user_invite_list.invite_id,
  status_invite: USER_CONSTANTS.user_invite_list.status_invite,
  user_sender_id: USER_CONSTANTS.user_invite_list.user_sender_id,
  name_sender: USER_CONSTANTS.user_invite_list.name_sender,
  email_sender: USER_CONSTANTS.user_invite_list.email_sender,
  organization_name: USER_CONSTANTS.user_invite_list.organization_name,
}

const getStatusIcon = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return <MailWarning className="size-5 text-secondary" />
    case "negado":
      return <MailX className="size-5 text-accent" />;
    default:
      return <Mail className="size-4 text-secondary" />;
  }
};

const getStatusText = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return "Pendente";
    case "negado":
      return "Negado";
    default:
      return status || "Desconhecido";
  }
};

const renderTextStyleFromStatus = (status: string | undefined) => {
  switch (status) {
    case "enviado":
      return "font-semibold";
    case "negado":
      return "font-normal opacity-50";
    default:
      return "font-normal";
  }
};

const inviteColumns: Column<UserInviteResponse>[] = [
  {
    key: columnProps.status_invite,
    header: "Status",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-2">
        {getStatusIcon(row.status_invite)}
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {getStatusText(row.status_invite)}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.email_sender,
    header: "Enviado por",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-2">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.email_sender || "N/A"}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.name_sender,
    header: "Nome",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-4">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.name_sender || "N/A"}
        </Text>
      </div>
    ),
  },
  {
    key: columnProps.organization_name,
    header: "Organização",
    widthClass: "w-1/4 min-w-[120px]",
    render: (_, row: UserInviteResponse) => (
      <div className="flex items-center gap-4">
        <Text className={renderTextStyleFromStatus(row.status_invite)}>
          {row.organization_name || "N/A"}
        </Text>
      </div>
    ),
  },
];


function OrganizationChangeConfirmation({
  currentOrganization,
  newOrganization,
}: OrganizationChangeConfirmationProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-4 bg-accent/10 border border-accent rounded-sm">
        <Building2 className="size-5 text-white flex-shrink-0" />
        <div>
          <Text variant="body-md" className="font-semibold">
            Atenção: Você está prestes a mudar de organização
          </Text>
          <Text variant="body-sm" className="mt-1">
            Ao aceitar este convite, você sairá da organização atual e entrará na nova organização.
          </Text>
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <Text variant="body-sm" className="font-medium text-secondary pb-1">
            Organização atual:
          </Text>
          <Text variant="body-md">
            {currentOrganization}
          </Text>
        </div>

        <div>
          <Text variant="body-sm" className="font-medium text-secondary pb-1">
            Nova organização:
          </Text>
          <Text variant="body-md">
            {newOrganization}
          </Text>
        </div>
      </div>

      <Text >
        Tem certeza de que deseja continuar?
      </Text>
    </div>
  );
}

function OrganizationChangeConfirmationFooter({ onConfirm, onCancel }: OrganizationChangeConfirmationFooterProps) {
  return (
    <div className="flex gap-3 justify-end">
      <Button variant="outline" onClick={onCancel}>
        Cancelar
      </Button>
      <Button variant="default" onClick={onConfirm}>
        Confirmar Mudança
      </Button>
    </div>
  );
}

export function InviteListDialogContent({ }: InviteListDialogContentProps) {
  const userInviteList = useUserInviteList();
  const { setUserInviteList } = useUserInviteListActions();
  const { userInviteQuery, answerInviteMutation, invalidadeUserInvite } = useUserCRUD();
  const { closeDialog, openDialog } = useDialogActions();
  const isOpen = useIsDialogIsOpen();
  const { data: invitesData, isFetching, refetch } = userInviteQuery("enviado");
  const userData = useUserData();

  useEffect(() => {
    if (invitesData) {
      setUserInviteList(invitesData);
    }
  }, [invitesData, setUserInviteList]);

  useEffect(() => {
    // TODO - mudar lógica para não fazer a requisição duas vezes /não precisar do refetch
    if (isOpen) {
      refetch();
    }
  }, [isOpen]);

  const handleAcceptInvite = (invite: UserInviteResponse) => {
    if (!invite.invite_id) {
      toast.error("ID do convite não encontrado");
      return;
    }

    const currentOrganization = userData?.organization_name;
    const inviteOrganization = invite.organization_name;

    if (currentOrganization && inviteOrganization && currentOrganization !== inviteOrganization) {
      const handleConfirm = () => {
        closeDialog(); // Necessário essa ação nesse ponto para não bloquear UI
        answerInviteMutation.mutate({
          accept_invite: true,
          invite_id: invite.invite_id!,
        }, {
          onSuccess: () => {
            toast.success("Convite aceito com sucesso!", {
              description: "Você será redirecionado para a página de login."
            });
          }
        });
      };

      const handleCancel = () => {
        openDialog({
          title: "Convites",
          icon: <Mail />,
          content: <InviteListDialogContent />,
          className: "max-w-4xl",
        });
      };

      openDialog({
        title: "Mudança de Organização",
        icon: <AlertTriangle />,
        content: (
          <OrganizationChangeConfirmation
            currentOrganization={currentOrganization}
            newOrganization={inviteOrganization}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
          />
        ),
        footer: (
          <OrganizationChangeConfirmationFooter
            onConfirm={handleConfirm}
            onCancel={handleCancel}
          />
        ),
        className: "max-w-lg",
      });
    } else {
      closeDialog(); // Necessário essa ação nesse ponto para não bloquear UI
      answerInviteMutation.mutate({
        accept_invite: true,
        invite_id: invite.invite_id,
      }, {
        onSuccess: () => {
          toast.success("Convite aceito com sucesso!", {
            description: "Você será redirecionado para a página de login."
          });
        }
      });
    }
  };

  const handleRejectInvite = (invite: UserInviteResponse) => {
    if (!invite.invite_id) {
      toast.error("ID do convite não encontrado");
      return;
    }

    answerInviteMutation.mutate({
      accept_invite: false,
      invite_id: invite.invite_id,
    }, {
      onSuccess: async () => {
        toast.success("Convite rejeitado com sucesso!");
        await invalidadeUserInvite();
      }
    });
  };


  const inviteActions = [
    {
      label: "Aceitar",
      onClick: handleAcceptInvite,
      icon: <CheckIcon />,
      disabled: answerInviteMutation.isPending
    },
    {
      label: "Rejeitar",
      onClick: handleRejectInvite,
      icon: <X />,
      disabled: answerInviteMutation.isPending
    }
  ];

  const renderContent = () => {
    const { checkPermission } = usePermissionCheck();
    const canAnswerInvite = checkPermission(Permission.ANSWER_INVITE);

    if (isFetching || answerInviteMutation.isPending) {
      return (
        <div className="flex items-center justify-center p-8">
          <Loading size="lg" />
        </div>
      );
    }

    if (!userInviteList || userInviteList.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center :max-w-4xl">
          <Mail className="size-12 text-gray-400 mb-4" />
          <Text variant="body-lg" className="font-semibold mb-2">
            Nenhum convite encontrado
          </Text>
          <Text variant="body-md" className="text-secondary">
            Você não possui convites pendentes no momento.
          </Text>
        </div>
      );
    }

    return (
      <DataTable
        columns={inviteColumns}
        data={userInviteList || []}
        actions={canAnswerInvite ? inviteActions : []}
        keyField="invite_id"
      />
    );
  };

  return (
    <div className="max-h-96 overflow-y-auto">
      {renderContent()}
    </div>
  );
}

export const InviteListDialog = {
  Content: InviteListDialogContent
};

export const OrganizationChangeDialog = {
  Content: OrganizationChangeConfirmation,
  Footer: OrganizationChangeConfirmationFooter
};