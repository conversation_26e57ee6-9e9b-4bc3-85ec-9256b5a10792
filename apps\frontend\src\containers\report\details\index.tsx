import { useEffect } from "react";
import { SecretKeyDialog } from "../SecretKeyDialog";
import { useModalControl, Loading } from "@snap/design-system";
import { KeyRound } from "lucide-react";
import { Navigate, useOutletContext, useParams } from "react-router";
import { BsFilePersonFill } from "react-icons/bs";
import { useUserData, useUserIsVerified } from "~/store/userStore";
import { getMockedData } from "root/modules/@snap/reports/ui/containers/report/details/getMockedData";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import {
  useReportSections,
  useReportDetailActions,
  useReportMetadata,
  useReportProfileImage,
  useReportType,
  useReportReloadTrigger
}
  from "~/store/reportDetailStore";
import { useReportActionsWithAutoSave } from "~/hooks/useReportActionsWithAutoSave";
import { ReportMetadata, ReportSection, OutletContextType } from "~/types/global";
import { __PersonDetailsPage } from "root/modules/@snap/reports/ui/containers/report/";
import TabContainer from "../TabContainer";
import { toast } from "sonner";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

const ReportDetailsContainer = () => {
  const { setMetadata, setReportSections, setReportType } = useReportDetailActions();
  const reportSections = useReportSections();
  const metadata = useReportMetadata();
  const profileImage = useReportProfileImage();
  const reportType = useReportType();
  const { open } = useModalControl();
  const isVerified = useUserIsVerified();
  const { setBreadcrumbs } = useOutletContext<OutletContextType>();
  const userData = useUserData();
  const detailsMock = getMockedData();
  const { id, type } = useParams<{ type: string; id: string }>();
  const actions = useReportActionsWithAutoSave()
  const { reportDetailsQuery } = useReportCRUD();
  const reloadTrigger = useReportReloadTrigger();
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);

  const { data: reportDetailsData, isLoading, isError, error } = reportDetailsQuery(id!);

  useEffect(() => {
    const loadReportData = () => {
      if (!reportDetailsData || isLoading) return;

      const { data: dataMap, ...meta } = reportDetailsData;
      const typeKey = type as keyof typeof dataMap;
      const foundSections: ReportSection[] = dataMap?.[typeKey] || [];

      setReportSections(foundSections);
      //@ts-ignore
      setMetadata(meta);
      setReportType(String(typeKey));

      setBreadcrumbs([
        {
          title: (meta[REPORT_CONSTANTS.new_report.report_name] as string) || "Relatório",
          icon: <BsFilePersonFill size={16} className="text-primary" />,
        },
      ]);

    };

    loadReportData();

    return () => {
      setBreadcrumbs([]);
    };
  }, [reportDetailsData, isLoading, reloadTrigger]);

  useEffect(() => {
    if (!isVerified && userData) {
      handleOpenUserSecretKeyDialog();
    }
  }, [isVerified, userData]);

  const handleOpenUserSecretKeyDialog = () => {
    open({
      modal: () => ({
        title: userData?.verifier ? "INSERIR SENHA" : "CRIAR SENHA",
        icon: <KeyRound />,
        content: <SecretKeyDialog.Content />,
        footer: <SecretKeyDialog.Footer onOpen={handleOpenUserSecretKeyDialog} />,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    });
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-screen w-full max-h-[calc(100vh-200px)]">
          <Loading size="lg" />
        </div>
      );
    }

    if (isError) {
      console.error("Error fetching report:", error);
      toast.error("Ocorreu um erro ao tentar carregar o relatório");
      return <Navigate to="/" replace />;
    };

    return (
      <div className={`flex px-8 flex-col lg:flex-row gap-8 w-full opacity-0 ${!isLoading && "opacity-100"} transition-opacity duration-200`}>
        <div className="flex-1 md:flex-1/4">
          <TabContainer />
        </div>
        <div className="flex-1 md:flex-3/4 max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable]">
          <__PersonDetailsPage
            store={{
              sections: reportSections,
              metadata: metadata as ReportMetadata,
              image: profileImage as string,
              reportType: reportType,
              isTrashEnabled: canUpdateReport,
              isPrintEnabled: true,
              actions
            }}
          />
        </div>
      </div>
    )
  }

  return renderContent();
};

export default ReportDetailsContainer;
