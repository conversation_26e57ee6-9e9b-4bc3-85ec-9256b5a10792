import logging
import unicodedata
import re

logger = logging.getLogger(__name__)

def normalize_text(text: str) -> str:
    """
    Lowercase, remove accents, strip special characters (except space).
    """
    logger.info("[normalize_text] Normalizing input text: %s", text)
    text = text.lower()
    text = ''.join(
        c for c in unicodedata.normalize('NFKD', text)
        if not unicodedata.combining(c)
    )
    text = re.sub(r'[^a-z0-9\s]', '', text)
    logger.info("[normalize_text] Result after normalization: %s", text)
    return text

def make_default_report_name(report_type: str, count: str) -> dict:
    """
    Create a default report name based on report type and count.
    """
    logger.info("[make_default_report_name] Creating default name for report type: %s, count: %s", report_type, count)
    return {
        "value": f"{report_type.capitalize()} {count}",
        "label": "Nome do relatório",
        "source": ["system"],
        "is_deleted": False
    }


