import logging

from models.base import Base
from models.user_model import Users
from models.report_model import UserReports
from models.invite_model import Invite
from models.organization_model import Organizations
from models.organization_users_model import OrganizationUsers
from models.user_columns_hmac import UserColumnsHmac

# Setup logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s]: %(message)s")

# List all models to be imported easily
__all__ = ["Users","Organizations","OrganizationUsers", "Invite", "UserReports", "UserColumnsHmac"]

logger.info("[models] Models initialized: Users, Organizations, OrganizationUsers, Invite, UserReports, UserColumnsHmac")
