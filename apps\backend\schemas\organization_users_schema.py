from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel
from enum import Enum
from core.constants import Status

class UserStatus(str, Enum):
    ACTIVE = Status.ativo
    INACTIVE = Status.inativo

class OrganizationUserBase(BaseModel):
    organization_id: UUID
    user_id: UUID
    status: UserStatus
    joined_at: datetime
    exited_at: Optional[datetime] = None
    validate_until: datetime

class OrganizationUserCreate(BaseModel):
    organization_id: UUID


class OrganizationUserUpdate(BaseModel):
    status: Optional[UserStatus] = None
    exited_at: Optional[datetime] = None
    validate_until: Optional[datetime] = None

class OrganizationUserInDB(OrganizationUserBase):
    organization_users_id: UUID

    class Config:
        from_attributes = True

class OrganizationUserResponse(OrganizationUserInDB):
    pass
