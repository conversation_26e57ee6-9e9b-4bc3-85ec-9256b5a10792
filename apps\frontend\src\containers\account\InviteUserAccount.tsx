import { Button, Input, Text } from '@snap/design-system'
import { ArrowRight } from 'lucide-react'
import { useState } from 'react';
import { toast } from 'sonner';
import { Checkbox } from '~/components/ui/checkbox';
import { Label } from '~/components/ui/label';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import { REPORT_TYPES } from '~/helpers/constants';
import { ReportCredits } from '~/types/global';
import {
  useUserCRUD,
} from '~/hooks/useUserCRUD';
import { parseValue } from '~/helpers';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

const InviteUserAccount = () => {
  const { createUserInviteMutation } = useUserCRUD();
  const [email_invited, setEmailInvited] = useState("")
  const [type_invite, setTypeInvite] = useState("investigador") // investigador, administrador,
  const initialReportTypes = {
    cpf: true,
    cnpj: true,
    email: true,
    telefone: true,
    relacoes: true,
  }
  const [report_types, setReportTypes] = useState(initialReportTypes)
  const [credits_sent, setCreditsSent] = useState<number>(100)
  const options = Object.values(REPORT_TYPES as ReportCredits | object).map(
    ([value]) => ({
      value: value,
      label: parseValue(value).toUpperCase(),
    })
  );

  const resetInviteSTate = () => {
    setEmailInvited("")
    setTypeInvite("investigador")
    setReportTypes(initialReportTypes)
    setCreditsSent(100)
  }

  const handleReportTypeChange = (type: string, checked: boolean) => {
    setReportTypes((prev) => ({ ...prev, [type]: checked }))
  }

  const handleSendInvite = () => {
    if (!email_invited || !credits_sent || !type_invite || !Object.values(report_types).some((type) => type)) {
      toast.error('Erro ao enviar convite!', {
        description: 'Campos Email, tipo de usuário, tipo de relatório e créditos não podem estar vazios.'
      });
      return;
    }
    const reportTypesArray = Object.keys(report_types).filter((type) => report_types[type as keyof typeof report_types])
    createUserInviteMutation.mutateAsync({
      email_invited,
      credits_sent: credits_sent.toString(),
      report_types: reportTypesArray,
      type_invite,
    }, {
      onSuccess: () => {
        resetInviteSTate()
      }
    });
  }

  return (
    <div className="flex flex-col gap-8 justify-center p-4">
      <Text variant="body-lg" className="font-semibold">
        Coloque o <span className='text-accent'> e-mail do usuário</span> que deseja convidar e defina seu tipo de acesso:
      </Text>

      <div className='flex gap-4 border border-border rounded-md p-8'>
        <div className="flex items-center flex-1 gap-4">
          <Input
            type="email"
            placeholder='Insira o e-mail aqui'
            variant='filled'
            value={email_invited}
            onChange={(e) => setEmailInvited(e.target.value)}
            className='rounded-none border-0 py-1.5 bg-background'
            wrapperClassName='flex-1 max-w-lg border-1 border-foreground'
          />

          <RadioGroup value={type_invite} onValueChange={setTypeInvite} className="flex flex-1 gap-8">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="investigador" id="investigador" className="border-white" />
              <Label htmlFor="investigador" className="text-[18px]">
                Investigador
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="administrador" id="administrador" className="border-white" />
              <Label htmlFor="administrador" className="text-[18px]">
                Administrador
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="space-y-8">
        <Text variant="body-lg" className="font-semibold">
          Marque os <span className="text-accent">tipos de relatório</span> que esse usuário terá acesso:
        </Text>

        <div className="flex space-between flex-wrap gap-4">
          {options.map((item) => {
            const checked = report_types[item.value as keyof typeof report_types]
            return (
              <label
                key={item.value}
                htmlFor={item.value}
                className={`
                  flex-1
                  flex items-center justify-between gap-2
                  bg-radio-button py-2 px-3 rounded
                  hover:opacity-100
                  ${checked ? "" : "opacity-80"}
                  cursor-pointer
                `}
              >
                <span className="font-mono text-[18px]">
                  {item.label}
                </span>

                <Checkbox
                  id={item.value}
                  checked={checked}
                  onCheckedChange={(c) =>
                    handleReportTypeChange(item.value, c as boolean)
                  }
                  className="border-[#D9D9D9] data-[state=checked]:bg-white rounded-none"
                />
              </label>
            )
          })}
        </div>
      </div>

      <div className="flex items-center gap-8 border-t border-b border-dashed border-border py-5">
        <Text variant="body-lg" className="font-semibold">
          Defina a quantidade de <span className="text-accent">consultas</span> para uso desse usuário:
        </Text>

        <Input
          type="number"
          variant='filled'
          min={0}
          value={credits_sent}
          onChange={(e) => setCreditsSent(Number(e.target.value))}
          className='rounded-none border-0 py-1.5 bg-background'
          wrapperClassName='flex-1 max-w-[100px] border-1 border-foreground'
        />
      </div>

      <Button
        variant="outline"
        onClick={handleSendInvite}
        iconPosition="right"
        className='w-full max-w-[264px]'
        icon={createUserInviteMutation.isPending ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <ArrowRight />}
        disabled={createUserInviteMutation.isPending}
      >
        ENVIAR CONVITE
      </Button>
    </div>
  )
}


export default InviteUserAccount