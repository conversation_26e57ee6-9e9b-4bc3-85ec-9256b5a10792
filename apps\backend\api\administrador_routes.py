import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession


from services.auth_service import auth_guard
from services.user_service import AdministradorService    

from database.db import get_db

from schemas.user_schema import OtherUser

from utils.jwt_utils import JWTUtils

from core.constants import Roles

from exceptions.business_exceptions import MissingEditUserAccessError

router = APIRouter()


@router.put("/update_invited_user/{other_user_id}")
async def update_other_user_data(
    other_user_id: str,
    other_user_data_after: OtherUser,
    db: AsyncSession = Depends(get_db),
    user=Depends(auth_guard)
):   

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    logger.info("[update_other_user_data] Function called by user %s for target user %s", user_id, other_user_id)
    logger.debug("[update_other_user_data] User roles: %s", user_roles)
    if Roles.edit_user in user_roles:
        admnistrador_service = AdministradorService(db=db, user_id=user_id)
        await admnistrador_service.set_user_to_modified_id(user_to_modified=other_user_id)
        await admnistrador_service.set_other_user(user_roles=user_roles, other_user_data_after=other_user_data_after)

    else:
        logger.warning("[update_other_user_data] Access denied - User %s does not have %s", user_id, Roles.edit_user)
        raise MissingEditUserAccessError()

