trigger:
  - staging

pool:
  name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  demands:
    - agent.name -equals SnapReportsStaging_Agent

steps:
  - script: |
      echo "Pulling latest code (force overwrite)"
      cd /home/<USER>/snapReportsStaging/Snap_Reports_10
      
      # Force reset to the latest staging branch
      git fetch --all
      git reset --hard origin/staging
      git clean -fd
    displayName: 'Pull latest from staging branch'

  - script: |
      echo "Running deploy script"
      cd /home/<USER>/snapReportsStaging/Snap_Reports_10
      chmod +x generate_and_deploy.sh
      ./generate_and_deploy.sh  --microsoft-tenant ${MICROSOFT_TENANT} --client-id-google $(CLIENT_ID_GOOGLE) --client-secret-google $(CLIENT_SECRET_GOOGLE) --client-id-microsoft $(CLIENT_ID_MICROSOFT) --client-secret-microsoft $(CLIENT_SECRET_MICROSOFT) --keycloak-admin-password $(KEYCLOAK_ADMIN_PASSWORD)   --snap-api-client-secret $(SNAP_API_CLIENT_SECRET) --captcha-key $(CAPTCHA_KEY)
    displayName: 'Run Docker Deploy Script'
