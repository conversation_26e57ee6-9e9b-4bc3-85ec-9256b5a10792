import { Text, Button } from "@snap/design-system";
import { Column, DataTable } from "~/components/Table";
import AccountToolbarUsers from "./AccountToolbarUsers";
import { Trash2, Edit, X, Eye } from "lucide-react";
import {
  useUserInvitesList,
  useUserInvitesTotalItems,
  useUserInvitesFilters,
  useUserInvitesActions
} from "~/store/userInvitesStore";
import { UserInviteResponse } from "~/types/global";
import { useDialogActions } from "~/store/dialogStore";
import { EditUserDialog } from "./EditUserDialog";
import { ViewInviteDetailsDialog } from "./ViewInviteDetailsDialog";
import { useEditUserActions } from "~/store/editUserStore";
import { USER_CONSTANTS } from "~/helpers/constants";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const columnProps = {
  email_invited: USER_CONSTANTS.user_invite_list.email_invited,
  type_invite: USER_CONSTANTS.user_invite_list.type_invite,
  status_invite: USER_CONSTANTS.user_invite_list.status_invite,
  sent_at: USER_CONSTANTS.user_invite_list.sent_at,
}

const AdminUserAccount = ({ }: UserConfigDialogContentProps) => {
  const invitesList = useUserInvitesList();
  const totalItems = useUserInvitesTotalItems();
  const filters = useUserInvitesFilters();
  const { setPage } = useUserInvitesActions();
  const { openDialog } = useDialogActions();
  const { clearEditUserValues } = useEditUserActions();
  const { cancelInviteMutation, removeUserFromOrganizationMutation } = useUserCRUD();
  const { checkPermission } = usePermissionCheck();
  const canViewUserDetails = checkPermission(Permission.GET_DATA_FROM_USER_INVITE);
  const canViewInviteDetails = checkPermission(Permission.GET_INVITE_DETAILS);
  const canCancelInvite = checkPermission(Permission.CANCEL_INVITE);
  const canRemoveUser = checkPermission(Permission.REMOVE_ORGANIZATION);

  const handleEditUser = (row: UserInviteResponse) => {
    openDialog({
      title: "Editar Usuário Convidado",
      icon: <Edit />,
      content: <EditUserDialog.Content selectedRow={row} />,
      footer: <EditUserDialog.Footer />,
      onClose: clearEditUserValues,
      className: "max-w-4xl",
    });
  };

  const handleViewDetails = (row: UserInviteResponse) => {
    openDialog({
      title: "Detalhes do Convite",
      icon: <Eye />,
      content: <ViewInviteDetailsDialog.Content selectedRow={row} />,
      footer: <ViewInviteDetailsDialog.Footer />,
      className: "max-w-4xl",
    });
  };

  const handleCancelInvite = (row: UserInviteResponse) => {
    cancelInviteMutation.mutate(row.invite_id || "");
  };

  const handleRemoveUser = (row: UserInviteResponse) => {
    removeUserFromOrganizationMutation.mutate(row.invite_id || "");
  };

  const getUserActions = (row: UserInviteResponse) => {
    const actions = [];
    const showDetails = row.status_invite !== USER_CONSTANTS.status_invite.aceito && row.status_invite !== USER_CONSTANTS.status_invite.desvinculado;
    const showEdit = row.status_invite === USER_CONSTANTS.status_invite.aceito
    const showCancel = row.status_invite === USER_CONSTANTS.status_invite.enviado;

    // Se o status é diferente de "aceito", mostra botão "Ver Detalhes"
    if (showDetails && canViewInviteDetails) {
      actions.push({
        label: "Ver Detalhes",
        onClick: handleViewDetails,
        icon: <Eye size={16} />
      });
    }

    // Se o status é "aceito", mostra botão de editar e excluir (remover da organização)
    if (showEdit && canViewUserDetails) {
      actions.push({
        label: "Editar",
        onClick: handleEditUser,
        icon: <Edit size={16} />
      });
    }
    if(showEdit && canRemoveUser) {
      actions.push({
        label: "Excluir",
        onClick: handleRemoveUser,
        icon: <Trash2 size={16} />
      });
    }

    // Se o status é "enviado", mostra botão de cancelar convite
    else if (showCancel && canCancelInvite) {
      actions.push({
        label: "Cancelar",
        onClick: handleCancelInvite,
        icon: <X size={16} />
      });
    }

    return actions;
  };

  const userColumns: Column[] = [
    {
      key: columnProps.email_invited,
      header: "Email do Usuário",
      widthClass: "w-2/5 min-w-[200px]",
      className: "overflow-hidden",
      render: (_, row: UserInviteResponse) => (
        <div className="flex items-center gap-4">
          <div className="truncate">
            <Text variant="body-md" className="font-semibold">{row.email_invited}</Text>
          </div>
        </div>
      ),
    },
    {
      key: columnProps.type_invite,
      header: "Perfil",
      widthClass: "w-1/5 min-w-[120px]",
      render: (_, row: UserInviteResponse) => (
        <div className="flex items-center gap-4">
          <Text variant="body-md" className="font-semibold">{row.type_invite}</Text>
        </div>
      ),
    },
    {
      key: columnProps.status_invite,
      header: "Status",
      widthClass: "w-1/5 min-w-[120px]",
      render: (_, row: UserInviteResponse) => (
        <div className="flex items-center gap-4">
          <Text variant="body-md" className="font-semibold">{row.status_invite}</Text>
        </div>
      ),
    },
    {
      key: columnProps.sent_at,
      header: "Data de Envio",
      widthClass: "w-1/5 min-w-[140px]",
      render: (_, row: UserInviteResponse) => (
        <div className="flex items-center gap-4">
          <Text variant="body-md" className="font-semibold">
            {row.sent_at ? new Date(row.sent_at).toLocaleDateString('pt-BR') : '-'}
          </Text>
        </div>
      ),
    },
    {
      key: "actions",
      header: "Ações",
      widthClass: "w-32",
      render: (_, row: UserInviteResponse) => (
        <div className="flex justify-end">
          {getUserActions(row).map((action, actionIndex) => (
            <div key={`action-${actionIndex}`} className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => action.onClick(row)}
                className="hover:opacity-80 !h-fit"
                title={action.label}
              >
                {action.icon}
              </Button>
              {actionIndex !== getUserActions(row).length - 1 && (
                <div className="w-px h-4 mx-2  border-l border-dotted border-foreground" />
              )}
            </div>
          ))}
        </div>
      ),
    }
  ];

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  return (
    <div className="flex flex-col flex-4/5">
      <AccountToolbarUsers />
      <div className="overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-386px)]">
        <DataTable
          columns={userColumns}
          data={invitesList}
          keyField={USER_CONSTANTS.user_invite_list.invite_id as keyof UserInviteResponse}
          actionsWidthClass="w-32"
          useFixedLayout={true}
          pagination={totalItems && totalItems > (filters.limit || 10) ? {
            pageSize: filters.limit || 10,
            totalItems: totalItems,
            currentPage: filters.page || 1,
            onPageChange: handlePageChange,
          } : undefined}
        />
      </div>
    </div>
  );
}

export default AdminUserAccount;