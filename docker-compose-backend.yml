services:
  backend:
    image: my-backend-image
    environment:
      KEY<PERSON>OAK_URL: ${KEYCLOAK_URL}
      CLIENT_ID_KEYCLOAK: ${CLIENT_ID_KEYCLOAK}
      CLIENT_SECRET_KEYCLOAK: /run/secrets/client_secret_keycloak
      DB_HOST: ${DB_HOST}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      BASE_API_URL: ${BASE_API_URL}
      REALM_NAME: ${REALM_NAME}
      FRONTEND_REDIRECT_URL: ${FRONTEND_REDIRECT_URL}
      SNAP_API_CLIENT_SECRET: /run/secrets/snap_api_client_secret
      CAPTCHA_KEY: /run/secrets/captcha_key
      REDIRECT_URI_KEYCLOAK: ${REDIRECT_URI_KEYCLOAK}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_CONTAINER_NAME: ${MINIO_CONTAINER_NAME}
      MINIO_S3_INTERNAL_PORT: ${MINIO_S3_INTERNAL_PORT}
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_EXTERNAL_URI: ${KAFKA_EXTERNAL_URI}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      SHOULD_USE_KAFKA_CONTAINER: ${SHOULD_USE_KAFKA_CONTAINER:-true}
      DELETE_PDFS_AFTER_DOWNLOAD: ${DELETE_PDFS_AFTER_DOWNLOAD:-true}
      LOG_LEVEL: ${LOG_LEVEL}

    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - kafka
    secrets:
      - snap_api_client_secret
      - captcha_key
      - client_secret_keycloak
    deploy:
      resources:
        limits:
          memory: 512M
      restart_policy:
        condition: on-failure
    networks:
      - mystack-net

#networks:
#  mystack-net:
#    external: true

#secrets:
#  snap_api_client_secret:
#    external: true
#  captcha_key:
#    external: true
#  client_secret_keycloak:
#    external: true
