CREATE SCHEMA IF NOT EXISTS keycloak AU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keycloak;

GRANT ALL PRIVILEGES ON SCHEMA keycloak TO keycloak;

ALTER ROLE keycloak SET search_path TO keycloak, public;

CREATE TYPE invite_status AS ENUM ('enviado', 'negado', 'aceito', 'cancelado', 'desvinculado');
CREATE TYPE user_status AS ENUM ('ativo', 'inativo');
CREATE TYPE organization_status AS ENUM ('ativo', 'inativo');
CREATE TYPE invite_type AS ENUM ('investigador', 'administrador');


CREATE TABLE IF NOT EXISTS public.users (
  "user_id" UUID NOT NULL,
  "image" TEXT NULL,
  "name" TEXT NULL,
  "credits" BIGINT NULL,
  "email" TEXT NULL,
  "last_login" TIMESTAMP WITH TIME ZONE NULL,
  "report_types" JSON NOT NULL DEFAULT '[]',
  "salt" TEXT NULL,
  "verifier" JSON NULL,
  "role" TEXT NOT NULL DEFAULT 'standalone', --standalone,investigator,manager
  "api_key" CHARACTER VARYING NULL,
  "is_deleted" BOOLEAN NOT NULL DEFAULT FALSE,
  "credits_monthly" BIGINT NULL,
  "next_reset_credits" TIMESTAMP WITH TIME ZONE NULL,
  CONSTRAINT users_pkey PRIMARY KEY ("user_id")
);

CREATE INDEX idx_users_email ON users(email);


CREATE TABLE IF NOT EXISTS public.organizations (
  "organization_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('UTC', now()),
  "name" CHARACTER VARYING NOT NULL,
  "image_logo" CHARACTER VARYING NULL,
  "api_key" CHARACTER VARYING NULL,
  "status_organization" organization_status NOT NULL,
  CONSTRAINT Organizations_pkey PRIMARY KEY ("organization_id")
);


CREATE TABLE IF NOT EXISTS public.organization_users(
  "organization_users_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "organization_id" UUID NOT NULL,
  "user_id" UUID NOT NULL,
  "status" user_status NOT NULL,   --ativo,inativo
  "joined_at" TIMESTAMP WITH TIME ZONE NOT NULL,
  "exited_at" TIMESTAMP WITH TIME ZONE NULL,
  "validate_until" TIMESTAMP WITH TIME ZONE NOT NULL,
  CONSTRAINT pk_organization_users PRIMARY KEY ("organization_users_id"),
  CONSTRAINT fk_organization_user_org FOREIGN KEY ("organization_id") REFERENCES public.organizations ("organization_id"),
  CONSTRAINT fk_organization_user_user FOREIGN KEY ("user_id") REFERENCES public.users ("user_id")
);

CREATE INDEX IF NOT EXISTS idx_organization_users_organization_id ON organization_users(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_users_user_id ON organization_users(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_users_status ON organization_users(status);

CREATE TABLE IF NOT EXISTS public.invite(
  "invite_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "user_sender_id" UUID NOT NULL,
  "organization_id" UUID NOT NULL,
  "status_invite" invite_status NOT NULL,
  "type_invite" invite_type NOT NULL,
  "report_types" JSONB NOT NULL,
  "credits_sent" INTEGER NOT NULL,
  "sent_at" TIMESTAMP WITH TIME ZONE NULL,
  "email_invited" TEXT NOT NULL,
  CONSTRAINT fk_invite_organization FOREIGN KEY ("organization_id") REFERENCES public.organizations ("organization_id"),
  CONSTRAINT fk_invite_user_sender FOREIGN KEY ("user_sender_id") REFERENCES public.users ("user_id")
);

CREATE INDEX idx_invite_user_sender_id ON invite(user_sender_id);
CREATE INDEX idx_invite_organization_id ON invite(organization_id);
CREATE INDEX idx_invite_status_invite ON invite(status_invite);
CREATE INDEX idx_invite_email_invited ON invite(email_invited);

CREATE TABLE IF NOT EXISTS public.report_types (
  "report_types_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('UTC', now()),
  "credits_required" INTEGER NOT NULL,
  "type" CHARACTER VARYING NOT NULL,
  CONSTRAINT ReportTypes_pkey PRIMARY KEY ("report_types_id")
);



CREATE TABLE IF NOT EXISTS public.user_reports (
  "user_reports_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "user_id" UUID NOT NULL,
  "organization_id" UUID NULL,
  "data" JSONB NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT timezone('UTC', now()),
  "modified_at" TIMESTAMPTZ NOT NULL DEFAULT timezone('UTC', now()),
  "report_name" JSONB NULL,
  "report_status" JSONB NULL,
  "report_type" TEXT NULL,
  "subject_name" JSONB NULL,
  "subject_mother_name" JSONB NULL,
  "subject_age" DATE NULL,
  "subject_sex" JSONB NULL,
  "subject_person_count" JSONB NULL,
  "subject_company_count" JSONB NULL,
  "report_search_args" JSONB NULL,
  "omitted_notes" JSONB NULL,
  "is_deleted" BOOLEAN NOT NULL DEFAULT FALSE,

  CONSTRAINT userReports_pkey PRIMARY KEY ("user_id", "user_reports_id"),
  CONSTRAINT userReports_userId_fkey FOREIGN KEY ("user_id") REFERENCES public.users ("user_id"),
  CONSTRAINT user_reports_id_unique UNIQUE ("user_reports_id"),
  CONSTRAINT userReports_organizationid_fkey FOREIGN KEY ("organization_id") REFERENCES public.organizations ("organization_id")
);


CREATE INDEX idx_user_reports_subject_age ON user_reports(subject_age);
CREATE INDEX idx_user_reports_created_at ON user_reports(created_at);
CREATE INDEX idx_user_reports_modified_at ON user_reports(modified_at);
CREATE INDEX idx_user_reports_report_type ON user_reports(report_type);
CREATE INDEX idx_user_reports_organization_id ON user_reports(organization_id);



CREATE TABLE IF NOT EXISTS public.user_columns_hmac (
  hmac TEXT NOT NULL,
  column_name TEXT NOT NULL,
  user_reports_id UUID NOT NULL REFERENCES public.user_reports(user_reports_id),
  PRIMARY KEY (hmac, column_name, user_reports_id)
);


CREATE INDEX idx_user_columns_hmac_hmac_column ON public.user_columns_hmac(hmac, column_name);

