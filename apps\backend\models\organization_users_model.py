from sqlalchemy import Column, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ENUM

from models.base import Base
from models.enums import UserStatus
from core.constants import TableNames

class OrganizationUsers(Base):
    __tablename__ = TableNames.organization_users
    __table_args__ = {"schema": "public"}

    organization_users_id = Column(PGUUID, primary_key=True)
    organization_id = Column(PGUUID, ForeignKey("public.organizations.organization_id"), nullable=False)
    user_id = Column(PGUUID, ForeignKey("public.users.user_id"), nullable=False)
    status = Column(
    ENUM(UserStatus, name="user_status", values_callable=lambda x: [e.value for e in x]),
    nullable=False
)
    joined_at = Column(DateTime(timezone=True), nullable=False)
    exited_at = Column(DateTime(timezone=True), nullable=True)
    validate_until = Column(DateTime(timezone=True), nullable=False)

    organization = relationship("Organizations", back_populates="users")
    user = relationship("Users", back_populates="organizations")

    def __repr__(self):
        return f"<OrganizationUsers(organization_id={self.organization_id}, user_id={self.user_id})>"
