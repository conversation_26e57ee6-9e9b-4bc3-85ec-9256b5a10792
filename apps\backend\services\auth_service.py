import logging
logger = logging.getLogger(__name__)

from urllib.parse import quote
from fastapi import Request, Response, HTTPException
import httpx

from core.jwt_utils import verify_jwt, refresh_access_token
from core.config import settings
from exceptions.business_exceptions import UserNotAuthenticatedError, FailRefreshTokenError, TokenExchangeFailedError


async def auth_guard(request: Request, response: Response) -> dict:
    logger.info("[auth_guard] Checking tokens in cookies...")
    access_token = request.cookies.get("access_token")
    refresh_token = request.cookies.get("refresh_token")

    if not access_token or not refresh_token:
        logger.error("[auth_guard] Missing access or refresh token.")
        raise UserNotAuthenticatedError()

    try:
        logger.info("[auth_guard] Verifying access token...")
        return await verify_jwt(access_token)

    except HTTPException as e:
        logger.error("[auth_guard] Token verification failed: %s", e.detail)
        logger.info("[auth_guard] Token expired. Attempting to refresh...")

        try:
            new_access_token, new_refresh_token = await refresh_access_token(refresh_token)
            logger.info("[auth_guard] Token refreshed successfully. Updating cookies...")
            response.set_cookie("access_token", new_access_token, httponly=True, secure=True, samesite="Strict")
            response.set_cookie("refresh_token", new_refresh_token, httponly=True, secure=True, samesite="Strict")

            logger.info("[auth_guard] Verifying new access token...")
            return await verify_jwt(new_access_token)

        except HTTPException as refresh_error:
            logger.error("[auth_guard] Refresh token process failed: %s", refresh_error.detail)
            raise FailRefreshTokenError()



async def exchange_code_for_tokens(code: str, frontend):
    logger.info("[exchange_code_for_tokens] Received code: %s", code)
    token_endpoint = "%s/realms/%s/protocol/openid-connect/token" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
    logger.info("[exchange_code_for_tokens] Token endpoint URL: %s", token_endpoint)

    logger.info("[exchange_code_for_tokens] Received frontend: %s", frontend)
    # frontend="http://localhost"
    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?frontend={quote(frontend)}"
    # redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}"
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": redirect_uri,
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
    }

    async with httpx.AsyncClient() as client:
        logger.info("[exchange_code_for_tokens] Sending POST request to Keycloak...")
        response = await client.post(token_endpoint, data=data)

    logger.info("[exchange_code_for_tokens] Response status code: %s", response.status_code)

    if response.status_code != 200:
        try:
            error_text = response.text
            logger.error("[exchange_code_for_tokens] Error response from Keycloak: %s", error_text)
        except Exception as e:
            logger.error("[exchange_code_for_tokens] Error reading response: %s", e)
        raise TokenExchangeFailedError(response.status_code, response.text)

    logger.info("[exchange_code_for_tokens] Successfully received tokens.")
    return response.json()

