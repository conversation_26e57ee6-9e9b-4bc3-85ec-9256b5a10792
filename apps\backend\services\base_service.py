# backend/services/base_service.py

from sqlalchemy.ext.asyncio import AsyncSession
import logging
logger = logging.getLogger(__name__)
class BaseService:
    """
    Uma classe base para serviços que fornece uma sessão de banco de dados.
    
    Atributos:
        db (AsyncSession): A sessão do banco de dados para ser usada nas operações.
    """


    def __init__(self, db: AsyncSession):
        """
        Inicializa o BaseService com uma sessão de banco de dados.
        
        Args:
            db (AsyncSession): A sessão assíncrona do banco de dados.
        """
        self.db = db
        self.user_id = None
        self.organization_id = None
        self.api_key = None
        self.api_credits= None
        self.api_next_reset_credits= None
        
        logger.info("[BaseService][__init__] Initialized BaseService with db session: %s", db)

    def set_user_id(self, user_id: str):
        self.user_id= user_id
        logger.info("[BaseService][set_user_id] Set user_id to: %s", user_id)

    def set_organization_id(self, organization_id: str):
        self.organization_id = organization_id
        logger.info("[BaseService][set_organization_id] Set organization_id to: %s", organization_id)

    def set_api_key(self, api_key: str):
        logger.info("[set_api_key] Setting API key for user: %s", self.user_id)
        logger.debug("[set_api_key] API key to set: %s", api_key)
        self.api_key=api_key
        logger.debug("[set_api_key] API key set successfully for user: %s", self.user_id)

        