# Folder Creation Store Documentation

## Overview

The `createFolderStore` is a Zustand store designed to manage folder creation state and tab navigation in the CreateReportDialog component. It follows the established patterns from other stores in the project and provides optimal performance through selective subscriptions.

## Features

- **Tab Management**: Centralized state for "report" vs "folder" tab selection
- **Folder Creation**: State management for folder name, selected reports, and creation status
- **Error Handling**: Built-in error state management
- **Performance Optimized**: Individual selectors to prevent unnecessary re-renders
- **TypeScript Support**: Fully typed with proper interfaces

## Store Structure

### State
```typescript
interface CreateFolderState {
  // Folder creation state
  folderName: string;
  selectedReports: string[];
  isCreating: boolean;
  error: string | null;
  
  // Tab state
  activeTab: CreateDialogTab; // "report" | "folder"
  
  // Actions
  actions: CreateFolderActions;
}
```

### Actions
- `setFolderName(name: string)`: Update folder name and clear errors
- `setSelectedReports(reportIds: string[])`: Set selected reports array
- `addSelectedReport(reportId: string)`: Add a report to selection
- `removeSelectedReport(reportId: string)`: Remove a report from selection
- `clearSelectedReports()`: Clear all selected reports
- `setIsCreating(isCreating: boolean)`: Set creation loading state
- `setError(error: string | null)`: Set error message
- `clearFolderData()`: Clear folder-specific data
- `setActiveTab(tab: CreateDialogTab)`: Switch between tabs
- `resetStore()`: Reset entire store to initial state

## Selectors (Hooks)

### Basic Selectors
```typescript
const folderName = useFolderName();
const selectedReports = useSelectedReports();
const isCreating = useIsCreatingFolder();
const error = useFolderError();
const activeTab = useActiveTab();
const actions = useCreateFolderActions();
```

### Computed Selectors
```typescript
const canCreateFolder = useCanCreateFolder(); // folderName.length > 0 && !isCreating
const hasSelectedReports = useHasSelectedReports(); // selectedReports.length > 0
```

## Usage Examples

### Basic Usage in Components
```typescript
import { 
  useFolderName, 
  useActiveTab, 
  useCreateFolderActions 
} from "~/store/createFolderStore";

function MyComponent() {
  const folderName = useFolderName();
  const activeTab = useActiveTab();
  const { setFolderName, setActiveTab } = useCreateFolderActions();
  
  return (
    <div>
      <button onClick={() => setActiveTab("folder")}>
        Create Folder
      </button>
      {activeTab === "folder" && (
        <input 
          value={folderName}
          onChange={(e) => setFolderName(e.target.value)}
        />
      )}
    </div>
  );
}
```

### With Folder Creation Hook
```typescript
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { useFolderName, useSelectedReports } from "~/store/createFolderStore";

function CreateFolderButton() {
  const { handleCreateFolder } = useFolderCRUD();
  const folderName = useFolderName();
  const selectedReports = useSelectedReports();
  
  const handleClick = () => {
    handleCreateFolder(folderName, selectedReports);
  };
  
  return <button onClick={handleClick}>Create Folder</button>;
}
```

## Integration with CreateReportDialog

The store is fully integrated with the `CreateReportDialog` component:

1. **Tab Navigation**: Centralized tab state shared between Content and Footer components
2. **Performance**: Uses `useCallback` and `useMemo` to prevent unnecessary re-renders
3. **Error Handling**: Displays validation errors inline
4. **Loading States**: Shows loading indicators during folder creation

## Backend Integration

The `useFolderCRUD` hook is prepared for backend integration:

- Currently includes a mock API call for demonstration
- Ready to be replaced with actual API endpoints
- Handles loading states and error management
- Integrates with TanStack Query for caching and invalidation

## Best Practices

1. **Use Specific Selectors**: Import only the state you need to prevent unnecessary re-renders
2. **Memoize Callbacks**: Use `useCallback` for event handlers that depend on store actions
3. **Reset on Cleanup**: Call `resetStore()` when closing dialogs or navigating away
4. **Error Handling**: Always check for errors and display them to users

## Migration from Previous Implementation

The new store replaces:
- Local `useState` for `folderName` and `activeTab`
- Global tab state management with listeners
- Manual prop passing between Content and Footer components

Benefits:
- Reduced component complexity
- Better performance through selective subscriptions
- Centralized state management
- Type safety
- Easier testing and debugging
