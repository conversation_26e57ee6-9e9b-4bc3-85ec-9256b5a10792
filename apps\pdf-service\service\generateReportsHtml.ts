import {renderToStaticMarkup} from 'react-dom/server';
import React from 'react';
import {ReportsSingleDocument} from "../components/ReportsSingleDocument";
import {REPORT_CONSTANTS} from "../config/constants";
import {IGenerateReportsHTMLInput, IGenerateReportsHTMLOutput} from "../global";
import {ReportsPdfHeader, wrapHeaderWithInlineCSS} from "../components/Header";
import {ReportsPdfFooter, wrapFooterWithInlineCSS} from "../components/Footer";

export async function GenerateReportsHtml(params: IGenerateReportsHTMLInput): Promise<IGenerateReportsHTMLOutput> {
  if (!params.sections || !Array.isArray(params.sections) || params.sections.length === 0) {
    throw new Error("Invalid sections provided in the request.");
  }
  if (!params.metadata) {
    throw new Error("Metadata is required in the request.");
  }

  const {sections, metadata, profile_image} = params;

  const title =
    (metadata[REPORT_CONSTANTS.new_report.report_name] as string) ||
    "Relatório";
  const reportDocument = React.createElement(ReportsSingleDocument, {
    sections,
    metadata,
    profile_image,
  });

  const fullHtml = renderToStaticMarkup(reportDocument);

  const headerHtml = renderToStaticMarkup(
    React.createElement(ReportsPdfHeader, {
      reportType: metadata[REPORT_CONSTANTS.new_report.report_type] as string,
      searchValue: Object.values(metadata[REPORT_CONSTANTS.new_report.report_search_args] as object)[0] as string,
      title: title,
    })
  );

  const footerHtml = renderToStaticMarkup(
    React.createElement(ReportsPdfFooter)
  );

  function wrapWithHtml(staticJsx: string): string {
    return `
        <!DOCTYPE html>
        <html lang="pt-br">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
        </head>
        <body>
        ${staticJsx}
        </body>
        </html>
        `.trim();
  }

  return {
    header: wrapHeaderWithInlineCSS(headerHtml),
    content: wrapWithHtml(fullHtml),
    footer: wrapFooterWithInlineCSS(footerHtml),
  };
}
