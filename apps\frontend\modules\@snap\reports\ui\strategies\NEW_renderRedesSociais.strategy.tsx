import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { isBase64Image, isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { GridItem, CustomLabel } from "@snap/design-system";
import { ValidatedImage } from "../components/ValidateImage";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";

// Interface for the new data structure
interface RedesSociaisEntry {
  detalhes: Array<{
    value: {
      [platform: string]: Array<{
        [field: string]: {
          value: any;
          label: string;
          source: string[];
          is_deleted: boolean;
        };
      }>;
    };
    label: string;
    source: string[];
    is_deleted: boolean;
  }>;
}

export function useRenderRedesSociais(
  sectionTitle: string
): ArrayRenderStrategy<RedesSociaisEntry> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<RedesSociaisEntry, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  // Test functions for deletion logic
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? e.detalhes.every((detalhe: any) => {
        if (!detalhe.value) return true;
        return Object.values(detalhe.value).every((platform: any) => {
          if (!Array.isArray(platform)) return true;
          return platform.every((profile: any) => {
            return Object.values(profile).every((field: any) => field.is_deleted === true);
          });
        });
      })
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    return testDetalhesDeleted(entry);
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Redes Sociais section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count each main platform object (badoo, facebook, twitter, etc.)
      return entry.detalhes.reduce((entryCount: number, detalhe: any) => {
        if (!detalhe.value) return entryCount;

        return entryCount + Object.keys(detalhe.value).reduce((platformCount: number, platform: string) => {
          const profiles = detalhe.value[platform];
          if (!Array.isArray(profiles)) return platformCount;

          // Count non-deleted profiles for this platform
          const nonDeletedProfiles = profiles.filter((profile: any) => {
            const allFieldsDeleted = Object.values(profile).every((field: any) => field.is_deleted === true);
            return !allFieldsDeleted;
          });

          return platformCount + (nonDeletedProfiles.length > 0 ? nonDeletedProfiles.length : 0);
        }, 0);
      }, count);
    }, 0);
  };

  // Helper functions for nested platform management
  const shouldIncludePlatform = (platform: any[]) => {
    return platform.some((profile: any) => {
      const fields = Object.values(profile);
      return isTrash
        ? fields.some((field: any) => field.is_deleted === true)
        : fields.some((field: any) => field.is_deleted === false);
    });
  };

  const shouldIncludeProfile = (profile: any) => {
    const fields = Object.values(profile);
    return isTrash
      ? fields.some((field: any) => field.is_deleted === true)
      : fields.some((field: any) => field.is_deleted === false);
  };

  const onTogglePlatformField = (
    entryIdx: number,
    detalheIdx: number,
    platform: string,
    profileIdx: number,
    fieldKey: string
  ) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const field = e.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx]?.[fieldKey];
          if (field) {
            field.is_deleted = !field.is_deleted;

            // Check if all fields in this profile are deleted
            const profile = e.detalhes[detalheIdx].value[platform][profileIdx];
            const allFieldsDeleted = Object.values(profile).every((f: any) => f.is_deleted === true);

            // If all fields are deleted, we consider the profile deleted
            // (no explicit is_deleted on profile level, but used for cascading logic)
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleProfile = (
    entryIdx: number,
    detalheIdx: number,
    platform: string,
    profileIdx: number
  ) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const profile = e.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx];
          if (profile) {
            // Determine target state based on current mode
            const targetDeletedState = isTrash ? false : true;

            // Apply to all fields in this profile
            Object.values(profile).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onTogglePlatform = (
    entryIdx: number,
    detalheIdx: number,
    platform: string
  ) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const platformProfiles = e.detalhes?.[detalheIdx]?.value?.[platform];
          if (platformProfiles && Array.isArray(platformProfiles)) {
            // Determine target state based on current mode
            const targetDeletedState = isTrash ? false : true;

            // Apply to all profiles and their fields in this platform
            platformProfiles.forEach((profile: any) => {
              Object.values(profile).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = targetDeletedState;
                }
              });
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: RedesSociaisEntry) => React.ReactElement | null
  > = {
    detalhes: (entry) => {
      if (!entry?.detalhes?.length) return null;
      const idx = idxMap.get(entry)!;

      const allElements: React.ReactElement[] = [];

      entry.detalhes.forEach((detalhe, detalheIdx) => {
        if (!detalhe.value) return;

        // Process each platform in this detalhe
        Object.entries(detalhe.value).forEach(([platform, profiles]) => {
          if (!Array.isArray(profiles) || !shouldIncludePlatform(profiles)) return;

          // Format platform name for display
          let platformName = platform.charAt(0).toUpperCase() + platform.slice(1);
          if (platform === "x (twitter)") platformName = "X (Twitter)";

          // Filter profiles to show
          const filteredProfiles = profiles
            .map((profile, profileIdx) => ({ profile, originalIdx: profileIdx }))
            .filter(({ profile }) => shouldIncludeProfile(profile));

          if (filteredProfiles.length === 0) return;


          allElements.push(
            <CustomGridContainer cols={1} key={`platform-${platform}-${detalheIdx}`} className="mb-6">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12"
                onToggleField={() => onTogglePlatform(idx, detalheIdx, platform)}
              >
                <ReportsCustomLabel
                  label={platformName.toUpperCase()}
                  colorClass="bg-primary"
                />
              </CustomGridItem>

              <CustomGridContainer cols={2}>
                {filteredProfiles.map(({ profile, originalIdx }, renderIdx) => (
                  <GridItem key={`${platform}-profile-${originalIdx}`} cols={1} >
                    <CustomGridItem
                      cols={1}
                      className="py-1"
                      containerClassName="w-fit pr-12"
                      onToggleField={() => onToggleProfile(idx, detalheIdx, platform, originalIdx)}
                    >
                      <ReportsCustomLabel
                        label={`${platformName.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      {Object.entries(profile)
                        .filter(([_, fieldValue]: any) =>
                          isTrash ? fieldValue.is_deleted : !fieldValue.is_deleted
                        )
                        .map(([fieldKey, fieldValue]: any) => {
                          const isImageValue = !Array.isArray(fieldValue.value) &&
                            (isValidUrl(fieldValue.value) || isBase64Image(fieldValue.value));
                          if (isImageValue) {
                            return (
                              <CustomGridItem
                                key={`${platform}-${originalIdx}-${fieldKey}`}
                                cols={1}
                                className="py-1"
                                onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                              >
                                <div className="py-2 group">
                                  <CustomReadOnlyInputField
                                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                    value={String(fieldValue.value)}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(fieldValue.source)}
                                  />
                                  {/* Renderiza a imagem seguindo a mesma lógica do campo */}
                                  <ValidatedImage
                                    src={String(fieldValue.value)}
                                    alt={`Imagem ${renderIdx + 1}`}
                                    className="w-full max-w-full h-48 mx-auto mt-2 bg-background/40 rounded-sm"
                                  />

                                </div>
                              </CustomGridItem>
                            );
                          }

                          // Handle array values (like vinculo)
                          if (Array.isArray(fieldValue.value)) {
                            const displayValue = fieldValue.value
                              .map((item: any) => item.rotulo || item)
                              .join(", ");

                            return (
                              <CustomGridItem
                                key={`${platform}-${originalIdx}-${fieldKey}`}
                                cols={1}

                                onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                              >
                                <CustomReadOnlyInputField
                                  label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                  value={displayValue}
                                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                  tooltip={renderSourceTooltip(fieldValue.source)}
                                />
                              </CustomGridItem>
                            );
                          }

                          // Default text field
                          return (
                            <CustomGridItem
                              key={`${platform}-${originalIdx}-${fieldKey}`}
                              cols={1}

                              onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={String(fieldValue.value || "")}
                                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          );
                        })}
                    </div>
                  </GridItem>
                ))}
              </CustomGridContainer>
            </CustomGridContainer>
          );
        });
      });

      return allElements.length > 0 ? <>{allElements}</> : null;
    },
  };

  const validateKeys = (keys: Array<keyof RedesSociaisEntry>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: RedesSociaisEntry): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof RedesSociaisEntry>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Redes Sociais] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: RedesSociaisEntry[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Redes Sociais] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return entry.detalhes?.some((detalhe: any) => {
          if (!detalhe.value) return false;
          return Object.values(detalhe.value).some((platform: any) => {
            if (!Array.isArray(platform)) return false;
            return platform.some((profile: any) => {
              return Object.values(profile).some((field: any) => field.is_deleted === true);
            });
          });
        });
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`redes-sociais-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe.value) {
              Object.values(detalhe.value).forEach((platform: any) => {
                if (Array.isArray(platform)) {
                  platform.forEach((profile: any) => {
                    Object.values(profile).forEach((field: any) => {
                      if (field && typeof field === 'object' && 'is_deleted' in field) {
                        field.is_deleted = targetDeletedState;
                      }
                    });
                  });
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
