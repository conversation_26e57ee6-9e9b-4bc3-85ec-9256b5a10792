FROM node:lts
WORKDIR /app

# Accept build arguments
ARG VITE_REPORTS_API_URL
ARG VITE_PDF_SERVICE_URL

# Set the build-time environment variables
ENV VITE_REPORTS_API_URL=${VITE_REPORTS_API_URL}
ENV VITE_PDF_SERVICE_URL=${VITE_PDF_SERVICE_URL}

# Install pnpm
RUN npm install -g pnpm@10.5.1

# Copy only package.json at the beginning
COPY apps/frontend/package.json ./

RUN echo "; begin auth token" > ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/registry/:username=victor.salles" >> ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/registry/:_password=QWprRm55eDFvMlV3em9kOFFBZWRCZDVBVjdhNFE2N2l2RzJoVWRJdDIxbUxRb2w1WUg0ZkpRUUo5OUJDQUNBQUFBQWFERmVNQUFBU0FaRE8zVXJZ" >> ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/registry/:email=<EMAIL>" >> ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/:username=victor.salles" >> ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/:_password=QWprRm55eDFvMlV3em9kOFFBZWRCZDVBVjdhNFE2N2l2RzJoVWRJdDIxbUxRb2w1WUg0ZkpRUUo5OUJDQUNBQUFBQWFERmVNQUFBU0FaRE8zVXJZ" >> ~/.npmrc
RUN echo "//forensedigital.pkgs.visualstudio.com/_packaging/DesignSystem/npm/:email=<EMAIL>" >> ~/.npmrc
RUN echo "; end auth token" >> ~/.npmrc

# Copy .npmrc file with pnpm settings
COPY apps/frontend/.npmrc ./

# Install dependencies with pnpm
RUN pnpm install

COPY apps/frontend ./

# Copy the constants folder from the monorepo root
COPY constants /app/constants

EXPOSE 3000

CMD ["pnpm", "run", "dev"]