from datetime import datetime, timezone
import json
import logging
import os
from copy import deepcopy
import boto3
from io import BytesIO
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import col, from_json
from pyspark.sql.types import StructType, <PERSON>ructField, StringType, ArrayType
import botocore.exceptions
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

CONTANTS_PATH = os.path.join(os.path.dirname(__file__), 'nomes_genericos.json')
MINIO_ENDPOINT = os.environ.get('MINIO_ENDPOINT', 'http://minio:9000')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', 'admin')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', 'password')
SPARK_CHECKPOINT_REPORTS = os.environ.get('SPARK_CHECKPOINT_REPORTS', '/tmp/spark_checkpoint_reports')

_constants = None
masked = False
spark = None

def load_constants():
    """
    Carrega o arquivo de constantes.
    """
    global _constants
    if _constants is None:
        with open(CONTANTS_PATH, "r", encoding="utf-8") as f:
            _constants = json.load(f)


def get_constant(key, default=None):
    if _constants is None:
        load_constants()
    return _constants.get(key, default)


def get_dtype(df, colname):
    """
    Retorna o tipo dos dados da coluna
    """
    return df.schema[colname].dataType


def group_entities(df):
    """
    Agrupa entidades presentes nos dados.
    Este metodo deve processar `reports_data` para agrupar informações relacionadas.
    """
    final_result = {}
    columns = df.columns
    for base_consultada in columns:
        if base_consultada == UsefulKeyDicts.metadata:
            break
        df_result_entity_inside = df.withColumn(base_consultada, F.explode(base_consultada))
        df_base_consultada = df_result_entity_inside.select(f"{base_consultada}.*")

        if 'msg' in df_base_consultada.columns:
            first = df_base_consultada.select("msg").first()
            if first.msg == ["Erro ao consultar base."]:
                continue

        for column_entity in df_base_consultada.columns:
            if not isinstance(get_dtype(df_base_consultada, column_entity), ArrayType):
                continue

            df_exploded = df_base_consultada.withColumn(column_entity, F.explode(column_entity))

            # Somente faz o select com "*" se for StructType
            if isinstance(get_dtype(df_exploded, column_entity), StructType):
                df_result = df_exploded.select(f"{column_entity}.*")
            else:
                df_result = df_exploded.select(column_entity)

            dict_result = ({column_entity: df_result.toJSON().map(lambda j: json.loads(j)).collect()})
            for key, value in dict_result.items():
                if final_result:
                    if final_result.get(key):
                        [final_result[key].append(data) for data in dict_result.get(key)]
                    else:
                        final_result.update(dict_result)
                else:
                    final_result.update(dict_result)

    return final_result


def organize_main_entity(all_entities_list, entity_type, type_search, type_search_arg, resultado_json):
    """
    Organiza a entidade principal dos dados.
    Este metodo deve estruturar a entidade principal para facilitar o acesso e manipulação.
    """
    global masked
    listain = []
    for k, v in deepcopy(all_entities_list).items():
        if k == entity_type:
            for index, lista_result in enumerate(v):
                for key, value in lista_result.items():
                    masked = False
                    if key == type_search and check_same_cnpj_cpf(type_search_arg, value, lista_result):
                        new_main_info = ({k: v for k, v in lista_result.items() if isinstance(v, str)})
                        new_entity_return = ({k: v for k, v in lista_result.items() if isinstance(v, list)})
                        if new_entity_return.get(k):
                            [all_entities_list[k].append(data) for data in new_entity_return.get(k)]

                        else:
                            for key_entity, value_entity in new_entity_return.items():
                                for data in value_entity:
                                    if all_entities_list.get(key_entity):
                                        all_entities_list[key_entity].append(data)
                                    else:
                                        all_entities_list[key_entity] = []
                                        all_entities_list[key_entity].append(data)
                        all_entities_list.update(new_main_info)
                        listain.append(index)
    for index in sorted(listain, reverse=True):
        del all_entities_list.get(entity_type)[index]
    reorganized_dict = {**{k: v for k, v in all_entities_list.items() if isinstance(v, str)},
                        **{k: v for k, v in all_entities_list.items() if isinstance(v, list)},  #
                        **{k: v for k, v in all_entities_list.items() if not isinstance(v, (str, list))}, }
    resultado_json[entity_type] = reorganized_dict
    return resultado_json


#
def check_same_cnpj_cpf(doc1: str, doc2: str, entity_dict: dict):
    global masked
    if '*' in doc1 or '*' in doc2:
        masked = True

    doc1 = ''.join(filter(str.isdigit, doc1))
    doc2 = ''.join(filter(str.isdigit, doc2))

    if masked:
        if doc1 in doc2 or doc2 in doc1:
            return True

    if doc1 == doc2:
        return True
    else:
        return False


class UsefulKeyDicts:
    # Definição das variáveis de strings para facilitar o uso em outras partes do código
    pessoa = "pessoa"
    empresa = "empresa"
    processo = "processo"
    cpf = "cpf"
    cnpj = "cnpj"
    reportType = "report_type"
    searchArgs = "report_search_args"
    metadata = "metadata"
    endpoint = "endpoint"
    data = "data"


def handle_batch(df, epoch_id):
    print(f'handling batch {epoch_id}')
    logger.info("[handle_batch] Handling batch %s", epoch_id)

    s3 = boto3.client("s3", endpoint_url=MINIO_ENDPOINT, aws_access_key_id=MINIO_ACCESS_KEY,
                      aws_secret_access_key=MINIO_SECRET_KEY)

    rows = df.collect()
    for row in rows:
        try:
            logger.info("[handle_batch] Trying to fetch bucket=%s, key=%s", row['bucket'], row['key'])
            obj = s3.get_object(Bucket=row['bucket'], Key=row['key'])
        except botocore.exceptions.ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logger.error("[handle_batch] File not found: %s/%s. Skipping...", row['bucket'], row['key'])
                continue
            else:
                raise

        file_content = obj['Body'].read().decode('utf-8')
        logger.debug("[handle_batch] Raw file content: %s", file_content)

        original_data = json.loads(file_content)

        substituicoes = get_constant("translateWord", {})
        for chave, novo_valor in substituicoes.items():
            logger.debug("[handle_batch] Replacing '%s' with '%s'", chave, novo_valor)
            file_content = file_content.replace(chave, novo_valor)

        reports_data = json.loads(file_content)
        logger.debug("[handle_batch] Decoded reports_data: %s", json.dumps(reports_data, indent=2))

        final_result = {}

        final_result["report_name"] = original_data.get("report_name")
        final_result["report_type"] = original_data.get("report_type")
        final_result["user_reports_id"] = original_data.get("user_reports_id")
        final_result["report_status"] = original_data.get("report_status")
        final_result["report_search_args"] = original_data.get("report_search_args")
        final_result["subject_name"] = original_data.get("subject_name")
        final_result["subject_mother_name"] = original_data.get("subject_mother_name")
        final_result["subject_age"] = original_data.get("subject_age")
        final_result["subject_sex"] = original_data.get("subject_sex")
        final_result["created_at"] = original_data.get("created_at")
        final_result["modified_at"] = original_data.get("modified_at")
        final_result["omitted_notes"] = original_data.get("omitted_notes")

        logger.debug("[handle_batch] Extracted original_data fields: %s", json.dumps(final_result, indent=2))

        type_search = reports_data.get(UsefulKeyDicts.reportType)
        logger.info("[handle_batch] Extracted type_search: %s", type_search)

        search_args = reports_data.get(UsefulKeyDicts.searchArgs, {})
        if not search_args:
            logger.error("[handle_batch] Missing '%s' in reports_data: %s, skipping", UsefulKeyDicts.searchArgs, json.dumps(reports_data, indent=2))
            continue

        # type_search_list = search_args.get(type_search.lower())
        # if not type_search_list:
        #     logger.error("[handle_batch] Missing search args for type '%s' in reports_data: %s", type_search.lower(), json.dumps(search_args, indent=2))
        #     continue

        # type_search_arg = extract_cpf_with_bookmark4(reports_data.get(UsefulKeyDicts.data))
        type_search_arg = search_args.get(type_search)[0]  # TODO this will work only when we have 1 argument, para relações da ruim

        logger.info("[handle_batch] Extracted type_search_arg: %s", type_search_arg)

        if type_search.lower() == UsefulKeyDicts.cpf:
            resultado_json = {UsefulKeyDicts.pessoa: {}}
            entity_type = UsefulKeyDicts.pessoa
        elif type_search.lower() == UsefulKeyDicts.cnpj:
            resultado_json = {UsefulKeyDicts.empresa: {}}
            entity_type = UsefulKeyDicts.empresa
        else:
            logger.error("[handle_batch] Unknown type_search value: %s", type_search)
            continue

        logger.info("[handle_batch] Entity type decided: %s", entity_type)

        data_inside = reports_data['data'][type_search][0]
        logger.debug("[handle_batch] Data inside: %s", json.dumps(data_inside, indent=2))

        json_data = json.dumps(data_inside)
        rdd = spark.sparkContext.parallelize([json_data])
        df = spark.read.option("multiline", "true").json(rdd)

        logger.info("[handle_batch] Starting grouping entities...")
        grouped_result = group_entities(df)
        logger.debug("[handle_batch] Grouped entities result: %s", json.dumps(grouped_result, indent=2))

        logger.info("[handle_batch] Starting organizing main entity...")
        processed_data = organize_main_entity(grouped_result, entity_type, type_search, type_search_arg, resultado_json)
        logger.debug("[handle_batch] Organized processed data: %s", json.dumps(processed_data, indent=2))

        final_result["data"] = {type_search: [processed_data]}

        logger.info("[handle_batch] Final full result ready to save")
        logger.debug("[handle_batch] Final full result: %s", json.dumps(final_result, indent=2))

        final_result_format = {"data": final_result}
        json_result = json.dumps(final_result_format)

        file_tmp_path = f'/tmp/processed-at{datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")}'
        with open(file_tmp_path, 'w') as f:
            f.write(json_result)

        logger.info("[handle_batch] Written processed file to temporary path: %s", file_tmp_path)

        byte_stream = BytesIO(json_result.encode('utf-8'))
        s3.put_object(Bucket="processed-reports", Key=row['key'], Body=byte_stream)

        logger.info("[handle_batch] Uploaded processed file to MinIO at bucket='processed-reports', key='%s'", row['key'])




def extract_cpf_with_bookmark4(data: dict) -> str | None:
    if not data or "cpf" not in data or not isinstance(data["cpf"], list):
        return None

    for cpf_entry in data["cpf"]:
        snap_entries = cpf_entry.get("SNAP")
        if isinstance(snap_entries, list):
            for snap_entry in snap_entries:
                pessoa_entries = snap_entry.get("pessoa")
                if isinstance(pessoa_entries, list):
                    for pessoa in pessoa_entries:
                        if pessoa.get("bookmark") == 4 and isinstance(pessoa.get("cpf"), str):
                            return pessoa["cpf"]
    return None


def main():
    global spark
    spark = SparkSession.builder.appName("KafkaMinIOProcessor").getOrCreate()

    # Configure MinIO access (S3 API)
    hadoop_conf = spark._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.endpoint", "http://minio:9000")
    hadoop_conf.set("fs.s3a.access.key", "admin")
    hadoop_conf.set("fs.s3a.secret.key", "password")
    hadoop_conf.set("fs.s3a.path.style.access", "true")
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")

    # Read messages from Kafka
    kafka_df = spark.readStream.format("kafka").option("kafka.bootstrap.servers", "kafka:9092").option("subscribe",
                                                                                                       "reports").option(
        "startingOffsets", "earliest").option("failOnDataLoss", "false").load()

    # .option("startingOffsets", "earliest") \

    # Define the schema to match the JSON structure
    schema = StructType([StructField("EventName", StringType()), StructField("Key", StringType()),  # root Key
                         StructField("Records", ArrayType(StructType([StructField("s3", StructType(
                             [StructField("bucket", StructType([StructField("name", StringType())])),
                              StructField("object", StructType([StructField("key", StringType())]))]))])))])

    raw_df = kafka_df.selectExpr("CAST(value AS STRING) as raw_json")
    parsed_df = raw_df.select(from_json(col("raw_json"), schema).alias("data"))

    # Now extract the actual key
    paths_df = parsed_df.select(col("data.Records")[0]["s3"]["bucket"]["name"].alias("bucket"),
                                col("data.Records")[0]["s3"]["object"]["key"].alias("key"))

    # json_df = kafka_df.selectExpr("CAST(value AS STRING) as json_str")
    # parsed_df = json_df.rdd.map(lambda row: json.loads(row.json_str)).toDF()

    paths_df.writeStream.foreachBatch(handle_batch).option("checkpointLocation", SPARK_CHECKPOINT_REPORTS).outputMode(
        "append").start().awaitTermination()


if __name__ == '__main__':
    main()
