from enum import Enum as PyEnum
from sqlalchemy import String, DateTime, Column, Enum as SAEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from datetime import datetime
import enum
from sqlalchemy.dialects.postgresql import UUID as PGUUID

from models.base import Base
from core.constants import Status, TableNames

class OrganizationStatus(str, PyEnum):
    ATIVO = Status.ativo
    INATIVO = Status.inativo

class Organizations(Base):
    __tablename__ = TableNames.organizations
    __table_args__ = {"schema": "public"}

    organization_id: Mapped[int] = mapped_column(
        PGUUID, primary_key=True, nullable=False
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, default=func.timezone('UTC', func.now())
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    image_logo: Mapped[str | None] = mapped_column(String, nullable=True)
    api_key: Mapped[str | None] = mapped_column(String, nullable=True)


    status_organization = Column(
        SAEnum(
            OrganizationStatus,
            name="organization_status",
            create_type=False,
            values_callable=lambda obj: [e.value for e in obj]
        ),
        nullable=False
    )


    users = relationship("OrganizationUsers", back_populates="organization")
    invites = relationship("Invite", back_populates="organization")

    def __repr__(self):
        return f"<Organizations(id={self.organization_id}, name='{self.name}')>"