Snap Reports Apps
This directory contains various applications and services that make up the Snap Reports system. Below is an overview of each subdirectory and its purpose:
Directories
1. Backend
Description: This directory contains the backend services for Snap Reports. These services handle the core business logic and data processing.
Technologies: [List any specific technologies or frameworks used, e.g., Node.js, Python, etc.]
2. Keycloak
Description: This directory is responsible for authentication and authorization services using Keycloak.
Technologies: Keycloak
3. Frontend
Description: This directory contains the frontend application for Snap Reports, providing the user interface and user experience.
Technologies: [List any specific technologies or frameworks used, e.g., React, Angular, etc.]
4. Spark
Description: This directory includes components related to Apache Spark, used for big data processing and analytics.
Technologies: Apache Spark
5. Minio
Description: This directory contains configurations and services for Minio, an object storage system.
Technologies: Minio
6. Kafka
Description: This directory includes components related to Apache Kafka, used for real-time data streaming.
Technologies: Apache Kafka
7. BFF (Backend for Frontend)
Description: This directory contains the Backend for Frontend services, which act as an intermediary between the frontend and backend services.
Technologies: Node.js
Getting Started
To get started with any of these services, navigate to the respective directory and follow the setup instructions provided in their individual README files.

4. To run all dockers:

For now it won't work locally, just on vm, because ngrok is pointing to the ip of ther vm on master, will give an error on it 
```bash
      ./generate_and_deploy.sh  --microsoft-tenant ${MICROSOFT_TENANT} --client-id-google $(CLIENT_ID_GOOGLE) --client-secret-google $(CLIENT_SECRET_GOOGLE) --client-id-microsoft $(CLIENT_ID_MICROSOFT) --client-secret-microsoft $(CLIENT_SECRET_MICROSOFT) --keycloak-admin-password $(KEYCLOAK_ADMIN_PASSWORD)   --snap-api-client-secret $(SNAP_API_CLIENT_SECRET) --captcha-key $(CAPTCHA_KEY)

Replace the variables values for .env


5. To run this create a pull request to our remote branch master, when the pull request be accept, will regenerate all the dockers (the first time take a time, the others is around 2 min to build all and 2 more minutes to all images be running).
The VM IS ***************
```

## Git Workflow

This project follows a structured Git workflow to ensure code quality and proper deployment procedures:

### Development Process

- **All new features, bug fixes, refactors, etc.** must be worked on a proper branch originated from the `develop` branch
- Use descriptive branch names with prefixes like `feat/`, `fix/`, or `hotfix/` when appropriate
- Follow semantic commit conventions for clear commit messages.

      - feat: (new feature for the user, not a new feature for build script)
      - fix: (bug fix for the user, not a fix to a build script)
      - docs: (changes to the documentation)
      - style: (formatting, missing semi colons, etc; no production code change)
      - refactor: (refactoring production code, eg. renaming a variable)
      - test: (adding missing tests, refactoring tests; no production code change)
      - chore: (updating grunt tasks etc; no production code change)

### Pull Request Process

1. **Feature/Bug Fix Completion**:
   - Create a pull request from your feature branch towards the `develop` branch
   - Use the **squash strategy** when merging
   - Delete the task branch after successful merge

2. **Staging Deployment**:
   - Create a pull request from `develop` to `staging` branch
   - Use **merge no fast forward** strategy
   - Ensure all tests pass before merging

3. **Production Deployment**:
   - Updates to `master` (production) must only be done after thorough testing on `staging`
   - Verify everything is working properly on staging environment
   - Create pull request from `staging` to `master` using **merge no fast forward**
   - **Exception**: Hotfixes can be merged directly from `develop` to `master` when immediate fixes are required

### Important Notes

- Always test thoroughly on staging before promoting to production
- Use semantic commit messages to enable automatic versioning and changelog generation
- Ensure pull requests include proper descriptions and link to relevant issues


