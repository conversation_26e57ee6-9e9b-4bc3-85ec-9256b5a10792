import logging

logger = logging.getLogger(__name__)

from fastapi import Request, HTTPException, APIRouter
from fastapi.responses import RedirectResponse

from services.user_service import sync_user
from services.auth_service import exchange_code_for_tokens

from core.config import settings

from urllib.parse import quote
from exceptions.business_exceptions import MissingAuthCallbackParameterError

router = APIRouter()


@router.get("/callback")
async def auth_callback(request: Request):
    logger.info("[auth_callback] Handling Keycloak callback...")
    logger.info("[auth_callback] Received request: %s", request.query_params)
    body = await request.body()
    logger.info("[auth_callback] Received body: %s", body.decode("utf-8"))
    code = request.query_params.get("code")
    logger.info("[auth_callback] Received code: %s", code)

    frontend_redirect = request.query_params.get("frontend")  # From dynamic redirect
    logger.info("[auth_callback] Redirecting back to: %s", frontend_redirect)

    if not code or not frontend_redirect:
        logger.error("[auth_callback] Missing authorization code in query params.")
        raise MissingAuthCallbackParameterError("code")

    logger.info("[auth_callback] Exchanging code for tokens...")
    tokens = await exchange_code_for_tokens(code, frontend_redirect)

    access_token = tokens.get("access_token")
    refresh_token = tokens.get("refresh_token")

    if not access_token:
        logger.error("[auth_callback] Failed to obtain access_token from Keycloak.")
        raise MissingAuthCallbackParameterError("access_token")
    if not refresh_token:
        logger.error("[auth_callback] Failed to obtain refresh_token from Keycloak.")
        raise MissingAuthCallbackParameterError("refresh_token")

    logger.info("[auth_callback] Tokens received successfully.")

    logger.info("[auth_callback] Syncing user with access token...")
    await sync_user(access_token=access_token)

    logger.info("[auth_callback] Preparing redirect to frontend: %s", frontend_redirect)
    redirect_response = RedirectResponse(url=frontend_redirect)

    logger.info("[auth_callback] Setting cookies.")
    redirect_response.set_cookie("access_token", access_token, httponly=True, secure=False, samesite="Lax", path="/")
    redirect_response.set_cookie("refresh_token", refresh_token, httponly=False, secure=False, samesite="Lax", path="/")

    logger.info("[auth_callback] Callback completed successfully.")
    return redirect_response


@router.get("/{provider}")
async def auth_redirect(request: Request, provider: str):
    logger.info("[auth_redirect] Initiating redirect for provider: %s", provider)

    logger.info("[auth_redirect] Full request URL: %s", request.url)
    logger.info("[auth_redirect] Request headers: %s", dict(request.headers))
    logger.info("[auth_redirect] HTTP Method: %s", request.method)
    client_ip = request.client.host
    logger.info("[auth_redirect] Client IP address: %s", client_ip)
    logger.info("[auth_redirect] Query parameters: %s", request.query_params)
    logger.info("[auth_redirect] Request scheme: %s", request.url.scheme)
    logger.info("[auth_redirect] Request hostname: %s", request.url.hostname)
    custom_header = request.headers.get("X-Forwarded-For")
    logger.info("[auth_redirect] X-Forwarded-For: %s", custom_header)
    logger.info("[auth_redirect] Request port: %s", request.url.port)
    logger.info("[auth_redirect] Request path: %s", request.url.path)

    origin = (request.query_params.get("redirect_url") or
              str(request.headers.get("origin")
                  or f"{request.url.scheme}://{request.url.hostname}"))

    logger.info("[auth_redirect] Received origin header: %s", origin)

    redirect_uri = f"{settings.REDIRECT_URI_KEYCLOAK}?frontend={quote(origin)}"
    logger.info("[auth_redirect] Received  redirect_uri: %s", redirect_uri)

    # You might want to validate providers here (optional)
    # allowed_providers = ["google", "microsoft"]
    # if provider not in allowed_providers:
    #     logger.warning(f"[auth_redirect] Unsupported provider attempted: {provider}")
    #     raise HTTPException(status_code=400, detail="Unsupported provider")

    auth_url = (
        f"{settings.KEYCLOAK_URL}/realms/{settings.REALM_NAME}"
        f"/protocol/openid-connect/auth?"
        f"client_id={settings.CLIENT_ID_KEYCLOAK}&"
        f"response_type=code&"
        f"scope=openid%20profile%20email&"
        f"redirect_uri={quote(redirect_uri)}&"
        f"kc_idp_hint={provider}"
    )

    logger.info("[auth_redirect] Generated auth URL: %s", auth_url)
    logger.info("[auth_redirect] Redirecting user to Keycloak for provider: %s", provider)

    return RedirectResponse(url=auth_url)
