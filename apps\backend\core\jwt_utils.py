import logging
import httpx
from jose import jwt, JW<PERSON>rror
from fastapi import Response

from core.config import settings
from exceptions.business_exceptions import (
    FailFetchJwkError,
    TokenVerificationFailedError,
    SessionExpiredError,
    FailRefreshTokenError,
    MissingRefreshTokenError,
    LogoutFailKeycloakError
)

logger = logging.getLogger(__name__)

_jwk_cache = {}

async def get_jwk_key(kid: str):
    global _jwk_cache
    logger.info("[get_jwk_key] Looking for key ID: %s", kid)

    if not _jwk_cache:
        logger.info("[get_jwk_key] JWK cache is empty. Fetching from Keycloak...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(settings.JWKS_URL)
            logger.info("[get_jwk_key] Response status: %s", response.status_code)

            response.raise_for_status()

            _jwk_cache = {jwk["kid"]: jwk for jwk in response.json()["keys"]}
            logger.info("[get_jwk_key] Keys cached: %s", list(_jwk_cache.keys()))

        except Exception as e:
            logger.exception("[get_jwk_key] Failed to fetch JWKs: %s", e)
            raise FailFetchJwkError()

    key = _jwk_cache.get(kid)
    if not key:
        logger.warning("[get_jwk_key] Key %s not found in cache. Refreshing...", kid)
        _jwk_cache = {}
        return await get_jwk_key(kid)

    logger.info("[get_jwk_key] Key %s found.", kid)
    return key


async def verify_jwt(token: str):
    try:
        logger.info("[verify_jwt] Extracting token header...")
        unverified_header = jwt.get_unverified_header(token)
        logger.info("[verify_jwt] Token algorithm: %s, kid: %s", unverified_header["alg"], unverified_header["kid"])

        jwk = await get_jwk_key(unverified_header["kid"])

        logger.info("[verify_jwt] Decoding token...")
        claims = jwt.decode(
            token,
            jwk,
            algorithms=[unverified_header["alg"]],
            audience=settings.CLIENT_ID_KEYCLOAK,
            issuer="%s/realms/%s" % (settings.KEYCLOAK_URL, settings.REALM_NAME)
        )
        logger.info("[verify_jwt] Token decoded successfully.")
        return claims

    except JWTError as e:
        logger.error("[verify_jwt] Token verification failed: %s", e)
        raise TokenVerificationFailedError()


async def refresh_access_token(refresh_token: str):
    logger.info("[refresh_access_token] Attempting to refresh token...")
    data = {
        "grant_type": "refresh_token",
        "client_id": settings.CLIENT_ID_KEYCLOAK,
        "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
        "refresh_token": refresh_token
    }

    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(settings.REFRESH_URL, data=data, headers=headers)

        logger.info("[refresh_access_token] Response status: %s", response.status_code)

        if response.status_code == 200:
            data = response.json()
            logger.info("[refresh_access_token] Token refreshed successfully.")
            return data["access_token"], data["refresh_token"]

        else:
            error_data = response.json()
            logger.warning("[refresh_access_token] Refresh token failed: %s", error_data)

            if "invalid_grant" in str(error_data).lower():
                logger.warning("[refresh_access_token] Max reuse exceeded. Forcing logout...")
                await logout_user_using_token(refresh_token=refresh_token)
                raise SessionExpiredError()

            raise FailRefreshTokenError()

    except Exception as e:
        logger.exception("[refresh_access_token] Unexpected error: %s", e)
        raise FailRefreshTokenError()


async def logout_user_using_token(refresh_token: str, response: Response = None):
    if not refresh_token:
        logger.error("[logout_user_using_token] Missing refresh token.")
        raise MissingRefreshTokenError()

    url = "%s/realms/%s/protocol/openid-connect/logout" % (settings.KEYCLOAK_URL, settings.REALM_NAME)

    try:
        async with httpx.AsyncClient() as client:
            res = await client.post(url, data={
                "client_id": settings.CLIENT_ID_KEYCLOAK,
                "client_secret": settings.CLIENT_SECRET_KEYCLOAK,
                "refresh_token": refresh_token
            })

        if res.status_code != 204:
            logger.error("[logout_user_using_token] Logout failed with status: %s", res.status_code)
            raise LogoutFailKeycloakError()

        logger.info("[logout_user_using_token] Logged out successfully.")

        if response:
            response.delete_cookie("refresh_token")
            response.delete_cookie("access_token")

        return {"message": "Logged out successfully"}

    except Exception as e:
        logger.exception("[logout_user_using_token] Unexpected error during logout: %s", e)
        raise LogoutFailKeycloakError()

