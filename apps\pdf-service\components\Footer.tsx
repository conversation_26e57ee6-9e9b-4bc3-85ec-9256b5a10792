import {GRAFISMO_BRANCO_SRC} from "../config/assets";
import {Text, View, Image} from "./pdf-components";
import {PdfStyles} from "./styles";

export const ReportsPdfFooter = () =>
  <View className={"footer"} style={PdfStyles.footer} fixed>
      <Image
        className={"footerSpiralImage"}
        src={GRAFISMO_BRANCO_SRC}
        style={PdfStyles.footerSpiralImage}
      />
    <View className={"footerContent"} style={PdfStyles.footerContent}>
      <div className={"pageNumber"} style={PdfStyles.pageNumber}></div>
      <Text  style={PdfStyles.pageNumber}> de </Text>
      <div className={"totalPages"} style={PdfStyles.pageNumber}></div>
    </View>
  </View>

export const wrapFooterWithInlineCSS = (html: string): string => {
  return `
    <style>${
    Object.entries(PdfStyles)
      .map(([key, value]) => `.${key} { ${Object.entries(value).map(([k, v]) => `${k}: ${v};`)
        .join(' ')} }`).join('\n')}
    }</style>
    ${html}
  `;
}
