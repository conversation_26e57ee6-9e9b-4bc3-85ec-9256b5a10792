from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, EmailStr, Field
from enum import Enum


from core.constants import InviteStatusCons, InviteTypeCons, ReportTypes

class InviteStatus(str, Enum):
    ENVIADO = InviteStatusCons.enviado
    NEGADO = InviteStatusCons.negado
    ACEITO = InviteStatusCons.aceito
    CANCELADO = InviteStatusCons.cancelado


class InviteType(str, Enum):
    INVESTIGADOR = InviteTypeCons.investigador
    ADMINISTRADOR = InviteTypeCons.administrador

class ReportType(str, Enum):
    cpf = ReportTypes.cpf
    cnpj = ReportTypes.cnpj
    email = ReportTypes.email
    telefone = ReportTypes.telefone
    combinado = ReportTypes.combinado
    relacao = ReportTypes.relacao


class InviteBase(BaseModel):
    user_sender_id: UUID
    organization_id: UUID
    status_invite: InviteStatus
    type_invite: InviteType
    email_invited: EmailStr
    sent_at: datetime = Field(default_factory=datetime.utcnow)
    credits_sent: int = 0
    report_types: Optional[List[ReportType]] = None
    

class InviteId(BaseModel):
    invite_id: UUID = Field(default_factory=UUID)


class InviteAnswer(InviteId):
    accept_invite: bool



class InviteCreate(BaseModel):
    email_invited: EmailStr
    credits_sent: int = 0
    report_types: Optional[List[ReportType]] = None
    type_invite: InviteType


class InviteIdOptional(BaseModel):
    invite_id: Optional[UUID] = None


class InviteUpdate(BaseModel):
    status_invite: Optional[InviteStatus] = None
    report_types: Optional[dict] = None



    class Config:
        from_attributes = True




