import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Imagem } from "../../model/Imagens";
import { ValueWithSource } from "../../model/ValueWithSource";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ValidatedImage } from "../components/ValidateImage";

export function useRenderImagens(sectionTitle: string): ArrayRenderStrategy<Imagem> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean => {
    return entry.detalhes
      ? entry.detalhes.every((detalhe: any) => {
          if (detalhe.value) {
            const properties = Object.keys(detalhe.value);
            const allPropertiesDeleted = properties.every(propertyKey =>
              detalhe.value[propertyKey]?.is_deleted === true
            );
            return allPropertiesDeleted || detalhe.is_deleted === true;
          }
          return detalhe.is_deleted === true;
        })
      : false;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted properties across all detalhes
      const propertyCount = entry.detalhes.reduce((detalheCount: number, detalhe: any) => {
        if (detalhe.value && detalhe.is_deleted !== true) {
          const properties = Object.keys(detalhe.value);
          const nonDeletedProperties = properties.filter(propertyKey =>
            detalhe.value[propertyKey]?.is_deleted !== true
          );
          return detalheCount + nonDeletedProperties.length;
        }
        return detalheCount;
      }, 0);

      return count + propertyCount;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (imagens?: Imagem) => React.ReactElement | null
  > = {
    detalhes: (imagens) => {
      if (!imagens?.detalhes?.length) return null;

      // Filter detalhes based on whether they contain properties that should be shown
      const filteredDetalhes = imagens.detalhes.filter((detalhe) => {
        if (detalhe.value) {
          // Check if this detalhe has any properties that should be included
          const properties = Object.keys(detalhe.value);
          return properties.some(propertyKey => {
            const propertyData = (detalhe.value as any)[propertyKey];
            const isPropertyDeleted = propertyData?.is_deleted === true;
            return shouldInclude(isPropertyDeleted);
          });
        }
        // If no value, check the detalhe's own is_deleted status
        return shouldInclude(detalhe.is_deleted === true);
      });

      if (filteredDetalhes.length === 0) return null;

      const onToggleDetalheProperty = (detalheIndex: number, propertyKey: string) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          entry => {
            const detalhe = (entry as any).detalhes?.[detalheIndex];
            if (detalhe?.value?.[propertyKey]) {
              detalhe.value[propertyKey].is_deleted = !detalhe.value[propertyKey].is_deleted;
            }
          },
          testEntryDeleted,
          testSectionDeleted,
          calculateDataCount
        );
      };

      return (


        <CustomGridContainer cols={4}>
          {(() => {
            let imageCounter = 1;

            return filteredDetalhes.map((detalhe) => {
              const originalIndex = imagens.detalhes.indexOf(detalhe);

              // Get all properties from detalhe.value and render each one
              const properties = detalhe.value ? Object.keys(detalhe.value) : [];

              return properties.map((propertyKey) => {
                const propertyData = (detalhe.value as any)[propertyKey];
                const imageValue = propertyData?.value;
                const isImageDeleted = propertyData?.is_deleted === true;
                const imageSource = propertyData?.source;

                // Only show if it should be included based on deletion state and mode
                if (!shouldInclude(isImageDeleted)) {
                  return null;
                }

                const currentImageNumber = imageCounter++;

                return (
                  <CustomGridItem
                    key={`imagem-detalhe-${originalIndex}-${propertyKey}`}
                    cols={1}
                    onToggleField={() => onToggleDetalheProperty(originalIndex, propertyKey)}
                  >
                    <div className="py-2 group">
                      <CustomReadOnlyInputField
                        label={`IMAGEM ${currentImageNumber}`}
                        value={String(imageValue || '')}
                        tooltip={renderSourceTooltip(imageSource)}
                      />
                      {/* Renderiza a imagem seguindo a mesma lógica do campo */}
                      {imageValue && (
                        <ValidatedImage
                          src={String(imageValue)}
                          alt={`Imagem ${currentImageNumber}`}
                          className="w-full max-w-full h-60 mx-auto mt-2 bg-background/40 rounded-sm"
                        />
                      )}
                    </div>
                  </CustomGridItem>
                );
              }).filter(Boolean); // Remove null entries
            }).flat();
          })()}
        </CustomGridContainer>


      );
    },
  };

  const validateKeys = (keys: Array<keyof Imagem>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (imagens: Imagem): React.ReactElement[] => {
    const keys = Object.keys(imagens) as Array<keyof Imagem>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Imagens] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(imagens))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Imagem[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Imagens] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((detalhe: any) => {
          if (detalhe.value) {
            // Check if any property within this detalhe is deleted
            const properties = Object.keys(detalhe.value);
            const hasDeletedProperty = properties.some(propertyKey =>
              detalhe.value[propertyKey]?.is_deleted === true
            );
            return hasDeletedProperty || detalhe.is_deleted === true;
          }
          return detalhe.is_deleted === true;
        });
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((imagens, index) => {
      const elements = renderSingleItem(imagens);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`imagens-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca todas as propriedades como deletadas/restauradas
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value) {
              const properties = Object.keys(detalhe.value);
              properties.forEach(propertyKey => {
                if (detalhe.value[propertyKey]) {
                  detalhe.value[propertyKey].is_deleted = targetDeletedState;
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
