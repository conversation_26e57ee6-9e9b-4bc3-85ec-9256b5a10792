import logging
logger = logging.getLogger(__name__)
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from core.config import settings
from models import Base
from exceptions.business_exceptions import DatabaseUnavailableError

MAX_RETRIES = 3
DATABASE_URL = settings.DATABASE_URL


def get_database_async_engine():
    _engine = create_async_engine(
        DATABASE_URL,
        echo=True,
        pool_pre_ping=True,
        pool_recycle=1800,
        connect_args={"server_settings": {"timezone": "UTC"}}
    )
    return _engine

engine = get_database_async_engine()
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def get_db():
    logger.debug("[get_db] Opening DB session")
    try:
        async with async_session() as session:
            yield session
    except SQLAlchemyError as e: # Catch only database-related errors
        # ------------------------
            logger.error("[get_db] Failed to create a database session: %s", e)
            # You might want to raise a 503 here, as the DB is unavailable
            raise DatabaseUnavailableError()
    finally:
        logger.debug("[get_db] Closing DB session")


async def init_db():
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            logger.info("[init_db] Database tables created successfully.")
    except Exception as e:
        logger.error("[init_db] Failed to initialize database: %s", e)
        raise

