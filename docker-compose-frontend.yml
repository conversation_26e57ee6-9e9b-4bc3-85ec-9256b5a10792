
services:
  frontend:
    image: my-frontend-image
    restart: always
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=development
      - VITE_REPORTS_API_URL=${VITE_REPORTS_API_URL}
      - VITE_PDF_SERVICE_URL=${VITE_PDF_SERVICE_URL}
      - NGINX_REPORTS_API_URL=${NGINX_REPORTS_API_URL}
      - NGINX_PDF_SERVICE_URL=${NGINX_PDF_SERVICE_URL}
    depends_on:
      - pdf
    deploy:
      resources:
        limits:
          memory: 512M
    networks:
      - mystack-net
