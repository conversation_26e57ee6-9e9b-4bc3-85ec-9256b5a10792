from reports_processor.EntityExtractor import EntityExtractor
from reports_processor.constants import ReportType
from reports_processor.reports.CNPJReportProcessor import CNPJReportProcessor
from reports_processor.reports.CPFReportProcessor import CPFReportProcessor
from reports_processor.reports.EmailReportProcessor import EmailReportProcessor
from reports_processor.reports.PhoneReportProcessor import PhoneReportProcessor


class ReportProcessorFactory:
    """Factory to create appropriate report processors"""

    @staticmethod
    def create_processor(extractor: EntityExtractor, report_type: ReportType):
        processors = {
            ReportType.CPF: CPFReportProcessor,
            ReportType.CNPJ: CNPJReportProcessor,
            ReportType.PHONE: PhoneReportProcessor,
            ReportType.EMAIL: EmailReportProcessor,
        }

        processor_class = processors.get(report_type)
        if not processor_class:
            raise ValueError(f"Unsupported report type: {report_type}")

        return processor_class(extractor)
