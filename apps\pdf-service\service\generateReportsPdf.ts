import {IGenerateReportsPDFInput} from "../global";
import * as logger from '../utils/logger';

export async function GenerateReportsPdf({
                                           browserRef: browser,
                                           ...params
                                         }: IGenerateReportsPDFInput): Promise<Uint8Array<ArrayBufferLike>> {
  if (!browser) {
    logger.error('Browser not initialized, cannot generate PDF');
    throw new Error('Browser not initialized');
  }

  try {
    logger.info('Starting PDF generation process');
    const startTime = Date.now();
    const page = await browser.newPage();
    logger.debug('New browser page created');

    await page.setViewport({width: 794, height: 1123});
    logger.debug('Viewport set to A4 dimensions');

    logger.debug('Content details', {
      headerLength: params.header?.length || 0,
      footerLength: params.footer?.length || 0
    });

    await page.setContent(params.content, {
      waitUntil: ['domcontentloaded', 'networkidle0'],
      timeout: 30000,
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      displayHeaderFooter: true,
      headerTemplate: params.header,
      footerTemplate: params.footer,
      printBackground: true,
      margin: {top: '84', right: '20', bottom: '74', left: '20'},
      preferCSSPageSize: true, // Use CSS @page size
      timeout: 1800000,
    });
    await page.close();

    const generationTime = Date.now() - startTime;
    logger.info('PDF generation completed', {
      duration: generationTime,
      size: pdfBuffer.length
    });

    return pdfBuffer;

  } catch (err) {
    logger.error('PDF generation error', {
      error: err as Error
    });
    throw new Error((err as Error).message);
  }
}
