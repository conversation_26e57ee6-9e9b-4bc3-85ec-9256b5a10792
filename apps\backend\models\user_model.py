from sqlalchemy import Column, BigInteger, Text, UUID, JSO<PERSON>, Boolean, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from models.base import Base
from core.constants import TableNames

class Users(Base):
    __tablename__ = TableNames.users
    __table_args__ = {"schema": "public"}

    user_id = Column(UUID, primary_key=True, index=True)
    image = Column(Text, nullable=True)
    name = Column(Text, nullable=True)
    credits = Column(BigInteger, nullable=True, default=0)
    email = Column(Text, nullable=True, index=True)
    last_login = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    report_types = Column(JSON, nullable=False, default= list)
    salt = Column(Text, nullable=True)
    verifier = Column(JSON, nullable=True)
    role = Column(Text, nullable=False)
    api_key = Column(String, nullable=True)
    is_deleted = Column(Boolean, nullable=False, default=False)
    credits_monthly = Column(BigInteger, nullable=True, default=0)
    next_reset_credits = Column(DateTime(timezone=True), nullable=True)

    organizations = relationship("OrganizationUsers", back_populates="user")
    sent_invites = relationship("Invite", back_populates="user_sender")

    def __repr__(self):
        return (
            f"<Users(user_id={self.user_id}, name={self.name}, email={self.email})>"
        )