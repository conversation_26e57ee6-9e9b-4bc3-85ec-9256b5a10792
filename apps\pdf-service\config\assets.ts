import path from 'path';
import { getImageAsDataUrl } from "../helpers";
import { determineAssetsPath } from "../helpers/assetUtils";

const assetsPath = determineAssetsPath();
const CPF_ICON_SRC = getImageAsDataUrl(path.join(assetsPath, 'print-cpf.png'));
const CNPJ_ICON_SRC = getImageAsDataUrl(path.join(assetsPath, 'print-cnpj.png'));
const LOGO_SRC = getImageAsDataUrl(path.join(assetsPath, 'pwa-192x192.png'));
const GRAFISMO_SRC = getImageAsDataUrl(path.join(assetsPath, 'grafismo.png'));
const GRAFISMO_BRANCO_SRC = getImageAsDataUrl(path.join(assetsPath, 'grafismo_branco.png'));
const MULTIPLE_USERS_ICON = getImageAsDataUrl(path.join(assetsPath, 'fa_users.png'));
const MULTIPLE_BUILDINGS_ICON = getImageAsDataUrl(path.join(assetsPath, 'fa_city.png'));

export {
  CPF_ICON_SRC,
  CNPJ_ICON_SRC,
  LOGO_SRC,
  GRAFISMO_SRC,
  GRAFISMO_BRANCO_SRC,
  MULTIPLE_USERS_ICON,
  MULTIPLE_BUILDINGS_ICON,
}
