import path from 'path';
import fs from 'fs';

/**
 * Determines the correct assets path based on the environment.
 * Checks for the existence of the assets directory in both development and production paths.
 * @returns The path to the assets directory
 */
export const determineAssetsPath = (): string => {
  const devPath = path.join(__dirname, '../assets');
  const prodPath = path.join(__dirname, '../dist/assets');
  
  if (fs.existsSync(devPath)) {
    return devPath;
  }
  
  if (fs.existsSync(prodPath)) {
    return prodPath;
  }
  
  console.warn('Assets directory not found. Using default path.');
  return devPath;
};