import React from 'react';
import { Accordion } from './CustomAccordion';
import { Badge } from './base/badge';
import { useNewStrategyMap } from '../strategies/NEW_ReportStrategyFactory';
import { useVisibleReportSections, useReportMode, useIsSaving, useIsTrashEnabled } from '../../context/ReportContext';
import { REPORT_SECTIONS } from '../../config/constants';
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";
import { Text } from '@snap/design-system';

const NEW_ReportDetailComponent: React.FC = () => {
  const mode = useReportMode();
  const isTrashEnabled = useIsTrashEnabled();
  const isTrashMode = mode === 'trash';
  const sections = useVisibleReportSections();
  const newMap = useNewStrategyMap();
  const isSaving = useIsSaving();
  console.log("mode: ", mode, "[NEW_ReportDetailComponent] SEÇÕES: ", sections);

  const triggerClassName = "bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer";

  return (
    <Accordion type="multiple">
      {sections.map((section, idx) => {
        // Pular subseções de processos (serão tratadas pela estratégia principal)
        if (
          section.title === REPORT_SECTIONS.processos &&
          !!section.subsection
        ) {
          return null;
        }

        // Seções com com estratégias novas (lixeira) tem prioridade
        const newStrategy = newMap[section.title];
        if (newStrategy) {
          const handleDeleteSection = () => {
            if (isSaving || !newStrategy.deleteSectionEntries) return;
            newStrategy.deleteSectionEntries();
          };

          return (
            <Accordion.Item key={`section-${idx}`} value={`section-${idx}`} className='border-b-0'>
              <div className="group">
                <Accordion.Trigger className={triggerClassName}>
                  <div className="flex items-center gap-4 w-full justify-between pr-8">
                    <div className="flex items-center gap-4">
                      <Text variant={isTrashMode ? "label-md" : "label-lg"} className="uppercase">
                        {section.title}
                      </Text>
                      {!isTrashMode && (
                        <Badge
                          className="rounded-2xl px-4 py-0.5 bg-accordion-badge hover:bg-accordion-badge border-0"
                        >
                          <Text className="text-foreground">{section.data_count}</Text>
                        </Badge>
                      )}
                    </div>
                    {newStrategy.deleteSectionEntries && isTrashEnabled && (
                      <span
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!isSaving) handleDeleteSection();
                        }}
                        title={isSaving ? "Salvando alterações..." : (isTrashMode ? "Restaurar seção" : "Deletar seção")}
                        className={`
                            flex items-center justify-center
                            w-10 h-10
                            opacity-0
                            group-hover:opacity-100
                            cursor-pointer
                            rounded-full
                            hover:bg-black/10
                            ${isSaving ? 'pointer-events-none cursor-wait' : ''}
                            transition-opacity duration-200
                          `}
                      >
                        {isTrashMode ? (
                          <LiaTrashRestoreAltSolid
                            size={32}
                            color={isSaving ? "var(--muted-foreground)" : "var(--foreground)"}
                          />
                        ) : (
                          <LiaTrashAltSolid
                            size={32}
                            color={isSaving ? "var(--muted-foreground)" : "var(--primary)"}
                          />
                        )}
                      </span>
                    )}
                  </div>
                </Accordion.Trigger>
              </div>
              <Accordion.Content className="px-5">
                <div className="pt-5">
                  {newStrategy.render(section.data).map((el: React.ReactNode, j: number) => (
                    <div key={j}>{el}</div>
                  ))}
                </div>
              </Accordion.Content>
            </Accordion.Item>
          );
        }

        // Caso não encontre estratégia, pular
        console.warn(`Sem estratégia de renderização para seção "${section.title}"`);
        return null;
      })}
    </Accordion>
  );
};

export default NEW_ReportDetailComponent;