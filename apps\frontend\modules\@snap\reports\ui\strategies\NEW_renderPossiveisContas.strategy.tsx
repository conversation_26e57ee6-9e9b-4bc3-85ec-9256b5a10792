import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { PossivelConta } from "../../model/PossiveisContas";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { Icon, GridContainer } from "@snap/design-system";

export function useRenderPossiveisContas(
  sectionTitle: string
): ArrayRenderStrategy<PossivelConta> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<PossivelConta, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testSiteDeleted = (e: any) => e.site?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;

  const testEntryDeleted = (entry: any): boolean => {
    const isSiteDeleted = testSiteDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);

    return isSiteDeleted && areDetalhesDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Possíveis Contas section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (entry?: PossivelConta) => React.ReactElement | null
  > = {
    site: (entry) => {
      if (!entry?.site || !includeKey(entry.site.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      // Determine icon and color based on 'existe' or 'found' value
      const existeValue = entry.detalhes?.existe?.value || entry.detalhes?.found?.value || "";
      const parsedValue = parseValue(String(existeValue));
      const imageIcon = parsedValue === "Sim" ? "/icons/icone_check.svg" : "/icons/icone_error.svg";
      const imageColor = parsedValue === "Sim" ? "text-green-400" : "text-red-500";

      return (
 
          <CustomGridItem
            fullWidth
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                // now only mutate the single entry at `idx`
                (e: any, i?: number) => {
                  if (i === idx && e.site) {
                    e.site.is_deleted = !e.site.is_deleted;

                    // Block deletion logic: when site is deleted, delete all other fields
                    // When site is restored, restore all other fields
                    const targetDeletedState = e.site.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.site.label.toUpperCase()}
              colorClass="bg-accent"
              labelTextClass="text-accent"
              value=""
              tooltip={renderSourceTooltip(entry.site.source)}
            />
            <div className="flex items-center gap-2 pt-2">
              {existeValue && (
                <Icon
                  src={imageIcon}
                  className={imageColor}
                />
              )}
              <CustomReadOnlyInputField
                value={String(entry.site.value || "")}
                tooltip={renderSourceTooltip(entry.site.source)}
                className="w-full"
              />
            </div>
          </CustomGridItem>
      
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={1}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          <div className="pl-5">
            {subs.map(([fieldKey, val]) => (
              <CustomGridItem
                key={fieldKey}
                cols={1}
               className="py-1"
                onToggleField={() =>
                  actions.updateSectionEntries!(
                    sectionTitle,
                    (e: any, i?: number) => {
                      if (i === idx) {
                        const d = e.detalhes?.[fieldKey];
                        if (d) d.is_deleted = !d.is_deleted;
                      }
                    },
                    testDetalhesDeleted,
                    testSectionDeleted,
                    calculateDataCount
                  )
                }
              >
                <CustomReadOnlyInputField
                  label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                  value={parseValue(String((val as any).value || ""))}
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  tooltip={renderSourceTooltip((val as any).source)}
                />
              </CustomGridItem>
            ))}
          </div>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof PossivelConta>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelConta): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelConta>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Possíveis Contas] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: PossivelConta[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Possíveis Contas] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return testSiteDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true));
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    // Render in two-column grid layout like the original
    if (filteredData.length > 1) {
      allElements.push(
        <CustomGridContainer cols={2} key="possiveisContas-grid">
          {filteredData.map((entry, index) => {
            const elements = renderSingleItem(entry);
            return (
              <div key={`conta-${index}`} >
                {elements}
              </div>
            );
          })}
        </CustomGridContainer>
      );
    } else {
      // Single item, render normally
      const elements = renderSingleItem(filteredData[0]);
      allElements.push(...elements);
    }

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.site) {
          entry.site.is_deleted = targetDeletedState;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
