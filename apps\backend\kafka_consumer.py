import json
import logging
import asyncio
import uuid

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from fastapi import FastAPI
from fastapi.encoders import jsonable_encoder

from core.config import settings
from core.constants import SummaryReportStatus
from services.minio_service import load_from_minio, store_temp_pdf_data

logger = logging.getLogger(__name__)

# PDF Consumer Configuration
PDF_REQUEST_TOPIC = "pdf-generation-requests"
PDF_REPLY_TOPIC = "pdf-generation-results"
PDF_GROUP_ID = "pdf-backend-responses"

# Global variables for PDF response consumer
_pdf_response_consumer = None
_pending_pdf_requests = {}

async def start_minio_event_consumer(app: FastAPI):
    logger.info("[start_minio_event_consumer] Starting MinIO Kafka Consumer...")
    logger.info(
        f"[start_minio_event_consumer] Setting Kafka consumer at"
        f" {settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT} or \n"
        f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}")

    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    consumer = AIOKafkaConsumer(
        "processed-reports",
        bootstrap_servers=kfk_servers,
        group_id="minio-ws-consumer"
    )
    await consumer.start()
    logger.info("[start_minio_event_consumer] MinIO Kafka Consumer started successfully.")

    try:
        async for msg in consumer:
            try:
                logger.info("[start_minio_event_consumer] Received message")
                
                payload = json.loads(msg.value.decode())

                logger.info(f"[start_minio_event_consumer] Received payload: {payload}" )

                record = payload.get("Records", [{}])[0]

                logger.info(f"[start_minio_event_consumer] Received record: {record}")

                key = record.get("s3", {}).get("object", {}).get("key")

                logger.info(f"[start_minio_event_consumer] Received event for key: {key}")
                # key = payload['Records'][0]['s3']['object']['key']

                if not key:
                    logger.warning(f"[start_minio_event_consumer] No object key found in message: {payload}",)
                    continue

                logger.info("[start_minio_event_consumer] Received event for key: %s", key)

                # Assume file name is user_id_reports_id.json
                if "_" not in key:
                    logger.warning(f"[start_minio_event_consumer] Invalid key format: {key}")
                    continue

                user_id, reports_id = key.replace(".json", "").split("_", 1)

                manager = app.state.connection_manager
                websocket = manager.get_connection(user_id)

                if not websocket:
                    logger.warning(f"[start_minio_event_consumer] No active WebSocket found for user_id={user_id}."
                                   f" Skipping processing.")
                    continue  # Skip deleting the report

                logger.info(f"[start_minio_event_consumer] Found WebSocket for user_id={user_id}."
                            f"Report_id={reports_id}>Loading report...")

                try:
                    result = await load_from_minio(
                        bucket_name="processed-reports",
                        object_name=f"{user_id}_{reports_id}.json",
                        user_id=user_id
                    )
                except Exception as e:
                    logger.exception(f"[start_minio_event_consumer] Error loading report {user_id}_{reports_id}.json"
                                     f" from minio - {e}")
                    continue

                try:
                    safe_result = jsonable_encoder(result)
                    logger.info("[start_minio_event_consumer] Safe Result=%s", safe_result.keys())

                    message = {
                        "id": reports_id,
                        "status_code": SummaryReportStatus.success,
                        "result": safe_result,
                    }

                    try:
                        await websocket.send_json(message)
                        logger.info(f"[start_minio_event_consumer] Sent report to user_id={user_id}, "
                                    f"reports_id={reports_id}, status = {message['status_code']}")

                    except Exception as send_err:
                        logger.warning(f"[start_minio_event_consumer] Failed to send WebSocket message: {send_err}")
                        # manager.disconnect(user_id)
                        continue  # DO NOT delete the report


                    # try:
                    #     await delete_from_minio(
                    #         bucket_name="processed-reports",
                    #         object_name=f"{user_id}_{reports_id}.json",
                    #         user_id=user_id
                    #     )
                    # except Exception as delete_err:
                    #     logger.warning(f"[start_minio_event_consumer] Failed to delete from MinIO: {delete_err}")

                except RuntimeError as send_err:
                    logger.warning(
                        "[start_minio_event_consumer] Failed to send WebSocket message: %s. Cleaning up.",
                        send_err
                    )
                    # manager.disconnect(user_id)

            except Exception as e:
                logger.exception(f"[start_minio_event_consumer] Error processing Kafka message - {e}")

    finally:
        logger.info("[start_minio_event_consumer] Stopping MinIO Kafka Consumer...")
        await consumer.stop()


async def start_pdf_event_consumer(app: FastAPI):
    """Optional: Start PDF response consumer proactively during app startup"""
    logger.info("[start_pdf_event_consumer] Starting PDF Kafka Consumer...")
    await _start_pdf_response_consumer()
    logger.info("[start_pdf_event_consumer] PDF Kafka Consumer started successfully.")


async def _start_pdf_response_consumer():
    """Start a single consumer for all PDF responses"""
    global _pdf_response_consumer, _pending_pdf_requests

    if _pdf_response_consumer is not None:
        return

    logger.info("[_start_pdf_response_consumer] Starting global PDF response consumer...")

    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    _pdf_response_consumer = AIOKafkaConsumer(
        PDF_REPLY_TOPIC,
        bootstrap_servers=kfk_servers,
        group_id=PDF_GROUP_ID,
        auto_offset_reset='latest',
        enable_auto_commit=True
    )
    await _pdf_response_consumer.start()

    # Start background task to process responses
    asyncio.create_task(_process_pdf_responses())
    logger.info("[_start_pdf_response_consumer] Global PDF response consumer started successfully")


async def _process_pdf_responses():
    """Background task to process all PDF responses"""
    global _pdf_response_consumer, _pending_pdf_requests

    try:
        async for msg in _pdf_response_consumer:
            try:
                result = json.loads(msg.value.decode())
                request_id = result.get("requestId")

                logger.info(f"[_process_pdf_responses] Received PDF response for request: {request_id}")

                if request_id in _pending_pdf_requests:
                    future = _pending_pdf_requests.pop(request_id)
                    if not future.done():
                        if result.get("status") == "error":
                            future.set_exception(Exception(f"PDF generation failed: {result.get('error', 'Unknown error')}"))
                        else:
                            future.set_result(result)
            except Exception as e:
                logger.exception(f"[_process_pdf_responses] Error processing PDF response: {e}")
    except Exception as e:
        logger.exception(f"[_process_pdf_responses] Error in PDF response processor: {e}")


async def send_pdf_request_and_wait(data, timeout=1200):
    """Send PDF request to Kafka and wait for response"""
    global _pending_pdf_requests

    # Ensure response consumer is running
    await _start_pdf_response_consumer()

    request_id = str(uuid.uuid4())

    logger.info(f"[send_pdf_request_and_wait] Sending PDF request with ID: {request_id}")

    # Store sensitive PDF data in MinIO temporarily
    data_reference = f"pdf-request-{request_id}"
    try:
        await store_temp_pdf_data(data_reference, data)
        logger.info(f"[send_pdf_request_and_wait] Stored PDF data in MinIO with reference: {data_reference}")
    except Exception as e:
        logger.error(f"[send_pdf_request_and_wait] Failed to store PDF data in MinIO: {e}")
        raise

    # Create future for this request
    future = asyncio.Future()
    _pending_pdf_requests[request_id] = future

    # Send only reference through Kafka (no sensitive data)
    user_context = data.get('user_context', {})
    kafka_message = {
        'requestId': request_id,
        'dataReference': data_reference,
        'metadata': {
            'report_type': user_context.get('report_type'),
            'user_reports_id': user_context.get('user_reports_id'),
            'user_id': user_context.get('user_id')
        },
        'timestamp': asyncio.get_event_loop().time()
    }

    # Send request
    kfk_servers = [f"{settings.KAFKA_CONTAINER_NAME}:{settings.KAFKA_INTERNAL_PORT}",
                   f"{settings.KAFKA_EXTERNAL_URI}:{settings.KAFKA_EXTERNAL_PORT}"]

    producer = AIOKafkaProducer(bootstrap_servers=kfk_servers)
    await producer.start()
    try:
        message_size = len(json.dumps(kafka_message))
        logger.info(f"[send_pdf_request_and_wait] Kafka message size: {message_size} bytes (vs original data size: {len(json.dumps(data))} bytes)")
        await producer.send_and_wait(PDF_REQUEST_TOPIC, json.dumps(kafka_message).encode(), key=request_id.encode())
        logger.info(f"[send_pdf_request_and_wait] PDF request sent, waiting for response: {request_id}")
    finally:
        await producer.stop()

    try:
        # Wait for response
        result = await asyncio.wait_for(future, timeout=timeout)
        logger.info(f"[send_pdf_request_and_wait] PDF request completed successfully: {request_id}")
        return result
    except asyncio.TimeoutError:
        # Clean up pending request
        _pending_pdf_requests.pop(request_id, None)
        logger.error(f"[send_pdf_request_and_wait] PDF generation timeout after {timeout} seconds for request: {request_id}")
        raise Exception(f"PDF generation timeout after {timeout} seconds")
    except Exception as e:
        # Clean up pending request
        _pending_pdf_requests.pop(request_id, None)
        logger.error(f"[send_pdf_request_and_wait] PDF generation error for request {request_id}: {e}")
        raise e