import * as React from "react";
import {
  <PERSON>alog as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>itle,
  <PERSON>alogDescription as ShadcnDialogDescription,
} from "~/components/ui/dialog";
import { cn } from "~/lib/utils";

interface DialogRootProps extends React.ComponentProps<typeof ShadcnDialog> {
  children: React.ReactNode;
}

interface DialogContentProps {
  children: React.ReactNode;
  className?: string;
}

interface DialogHeaderProps {
  children: React.ReactNode;
  headerBgColor?: string;
}

interface CustomIconProps {
  children: React.ReactNode;
  className?: string;
}

interface DialogFooterProps {
  children: React.ReactNode;
}

function DialogRoot({ children, ...props }: DialogRootProps) {
  return <ShadcnDialog {...props}>{children}</ShadcnDialog>;
}

function DialogContent({ children, className = "" }: DialogContentProps) {
  return (
    <ShadcnDialogContent
      className={cn(
        "w-full p-0 rounded-md shadow-md gap-0 align-top bg-modal-body",
        className
      )}
    >
      {children}
    </ShadcnDialogContent>
  );
}

function DialogHeader({ children, headerBgColor = "bg-modal-header" }: DialogHeaderProps) {
  return (
    <ShadcnDialogHeader className={`flex-row items-center justify-between p-4 rounded-t-lg ${headerBgColor}`}>
      {children}
    </ShadcnDialogHeader>
  );
}

function DialogTitle({ children }: { children: React.ReactNode }) {
  return (
    <ShadcnDialogTitle className="font-mono uppercase mb-0">
      {children}
    </ShadcnDialogTitle>
  );
}

export function DialogIcon({ children, className = "" }: CustomIconProps) {
  return <div className={`${className}`}>{children}</div>;
}

function DialogDesciption({ children }: { children: React.ReactNode }) {
  return <ShadcnDialogDescription className="text-accent-foreground text-md">{children}</ShadcnDialogDescription>;
}

function DialogBody({ children }: { children: React.ReactNode }) {
  return <div className="p-4">{children}</div>;
}

function DialogFooter({ children }: DialogFooterProps) {
  return (
    <ShadcnDialogFooter className="p-4">
      {children}
    </ShadcnDialogFooter>
  );
}

export const Dialog = Object.assign(DialogRoot, {
  Trigger: DialogTrigger,
  Content: DialogContent,
  Header: DialogHeader,
  Title: DialogTitle,
  Icon: DialogIcon,
  Description: DialogDesciption,
  Body: DialogBody,
  Footer: DialogFooter,
});
