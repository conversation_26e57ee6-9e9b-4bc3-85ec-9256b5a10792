import {StyleSheet} from "./pdf-components/StyleSheet";

export const PdfStyles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 12,
    paddingTop: 80,
    paddingBottom: 90,
    paddingHorizontal: 20,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 70,
    backgroundColor: '#E5E5EA',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    overflow: 'hidden',
    '-webkit-print-color-adjust': 'exact',
  },
  logoContainer: {
    paddingTop: 2,
    marginRight: 20
  },
  logo: {
    width: 24,
    height: 'auto'
  },
  headerContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center'
  },
  title: {
    fontSize: 16
  },
  reportTypeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  searchIcon: {
    width: 10,
    height: 10,
    marginRight: 4
  },
  searchValue: {
    fontSize: 10
  },
  spiralImage: {
    position: 'absolute',
    right: -110,
    top: -60,
    width: 300,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  customHeader: {
    paddingVertical: 10,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: "#889EA3",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    overflow: "hidden",
    '-webkit-print-color-adjust': 'exact',
  },
  footerSpiralImage: {
    position: 'absolute',
    left: -120,
    bottom: -148,
    width: 350,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  footerContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: "flex-end",
    alignItems: "flex-end",
    gap: 10,
  },
  pageNumber: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'normal',
  },
});
