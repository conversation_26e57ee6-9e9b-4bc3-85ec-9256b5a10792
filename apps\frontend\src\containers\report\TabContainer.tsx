import { useMemo } from "react";
import { cn } from "~/lib/utils";
import { Tabs } from '@snap/design-system'
import * as T from "../report/details/tabs"
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";


const TabContainer = () => {
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);

  const renderTabList = () => {
    const registriesTab = {
      value: 'registries',
      key: 'registries',
      label: 'REGISTROS',
      children: <T.ReportRecordsList />
    };
    const trashTab = {
      value: 'trash',
      key: 'trash',
      label: 'LIXEIRA',
      children: <T.ReportTrash />
    };

    if (!canUpdateReport) {
      return [
        registriesTab
      ];
    }

    return [
      registriesTab,
      trashTab
    ];
  }

  return (
    <Tabs items={renderTabList()} className="[&_[role=tab]]:cursor-pointer" />
  );
};

export default TabContainer;
