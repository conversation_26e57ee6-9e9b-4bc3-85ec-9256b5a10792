import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '../pdf-components';
import { ReportSection, ValueWithSource } from '../../global';
import { translatePropToLabel, getSingular, parseValue, translateSource } from '../../helpers';

interface RenderPrintMandadosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      numero?: ValueWithSource;
      pessoa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      processos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>;
  };
}

export const RenderPrintMandados: React.FC<RenderPrintMandadosProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} >
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((mandado, mandadoIndex) => (
        <View key={`mandado-${mandadoIndex}`} style={styles.mandadoContainer}>
          {/* Número do Mandado */}
          {mandado?.numero && !mandado.numero.is_deleted && (
            <View style={styles.numeroContainer} wrap={false}>
              <View style={styles.numeroLabelContainer}>
                <Text style={styles.numeroLabel}>
                  {(translatePropToLabel(mandado.numero.label || "Número do Mandado")).toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {mandado.numero.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.numeroValue}>{parseValue(mandado.numero.value)}</Text>
            </View>
          )}

          {/* Detalhes do Mandado */}
          {mandado?.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(mandado.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Pessoas */}
          {mandado?.pessoa && Array.isArray(mandado.pessoa) && mandado.pessoa.length > 0 &&
            mandado.pessoa.some(pessoa => !pessoa.is_deleted) && (
              <View style={styles.pessoaContainer}>
                <View style={styles.subtitleContainer}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>PESSOAS</Text>
                </View>
                <View style={styles.phoneGrid}>
                  {mandado?.pessoa
                    .filter(pessoa => !pessoa.is_deleted)
                    .map((pessoa, index) => (
                      <View key={`pessoa-${index}`} style={styles.phoneBlock} >
                        <View style={styles.listContainer}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(pessoa.label) || 'PESSOA').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {pessoa.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.fieldsGrid}>
                          {Object.entries(pessoa.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell}>
                                <View style={styles.infoContainer}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(fieldValue.value || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            )}

          {/* Processos */}
          {mandado?.processos && Array.isArray(mandado.processos) && mandado.processos.length > 0 &&
            mandado.processos.some(processo => !processo.is_deleted) && (
              <View style={styles.processosContainer}>
                <View style={styles.subtitleContainer}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>PROCESSOS</Text>
                </View>
                <View style={styles.phoneGrid}>
                  {mandado.processos
                    .filter(processo => !processo.is_deleted)
                    .map((processo, index) => (
                      <View key={`processo-${index}`} style={styles.phoneBlock} >
                        <View style={styles.listContainer}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(processo.label) || 'PROCESSO').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {processo.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.fieldsGrid}>
                          {Object.entries(processo.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell}>
                                <View style={styles.infoContainer}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(fieldValue.value || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            )}

          {/* Telefones */}
          {mandado.telefones && Array.isArray(mandado.telefones) && mandado.telefones.length > 0 &&
            mandado.telefones.some(telefone => !telefone.is_deleted) && (
              <View style={styles.telefonesContainer}>
                <View style={styles.subtitleContainer}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>TELEFONES</Text>
                </View>
                <View style={styles.phoneGrid}>
                  {mandado.telefones
                    .filter(telefone => !telefone.is_deleted)
                    .map((telefone, index) => (
                      <View key={`telefone-${index}`} style={styles.phoneBlock} >
                        <View style={styles.listContainer}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(telefone.label) || 'TELEFONE').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {telefone.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.fieldsGrid}>
                          {Object.entries(telefone.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell}>
                                <View style={styles.infoContainer}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(fieldValue.value || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  numeroLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  mandadoContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  numeroContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  numeroLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  numeroValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  pessoaContainer: {
    marginBottom: 8,
  },
  processosContainer: {
    marginBottom: 8,
  },
  telefonesContainer: {
    marginBottom: 8,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  // List styles (pessoa, processos, telefones)
  phoneGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  phoneBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
});