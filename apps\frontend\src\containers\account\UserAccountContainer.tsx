import AccountUserProfile from "./AccountUserProfile";
import TabContainerAccount from "./TabContainerAccount";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const UserAccountContainer = ({ }: UserConfigDialogContentProps) => {

  return (
    <div className="flex gap-8 px-8">
      <TabContainerAccount />

      <div className="flex flex-col flex-1/4 max-w-sm">
        <AccountUserProfile />
      </div>
    </div>
  );
}

export default UserAccountContainer;