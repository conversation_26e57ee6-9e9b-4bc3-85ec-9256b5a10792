import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { GridItem } from "@snap/design-system";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { PossivelContato } from "../../model/PossiveisContatos";

export function useRenderPossiveisContatos(
  sectionTitle: string
): ArrayRenderStrategy<PossivelContato> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<PossivelContato, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testNomeCompletoDeleted = (e: any) => e.nome_completo?.is_deleted === true;
  const testRazaoSocialDeleted = (e: any) => e.razao_social?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  const testPessoaDeleted = (e: any) =>
    e.pessoa
      ? e.pessoa.every((p: any) =>
        Object.values(p.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testTelefonesDeleted = (e: any) =>
    e.telefones
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEnderecosDeleted = (e: any) =>
    e.enderecos
      ? e.enderecos.every((end: any) =>
        Object.values(end.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;

  const testEntryDeleted = (entry: any): boolean => {
    const isNomeCompletoDeleted = testNomeCompletoDeleted(entry);
    const isRazaoSocialDeleted = testRazaoSocialDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const arePessoaDeleted = testPessoaDeleted(entry);
    const areTelefonesDeleted = testTelefonesDeleted(entry);
    const areEnderecosDeleted = testEnderecosDeleted(entry);

    return (isNomeCompletoDeleted || isRazaoSocialDeleted) && areDetalhesDeleted && arePessoaDeleted && areTelefonesDeleted && areEnderecosDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  const onToggleNestedField = (entryIdx: number, arrayKey: string, blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value?.[fieldKey]) {
            item.value[fieldKey].is_deleted = !item.value[fieldKey].is_deleted;

            const allFieldsDeleted = Object.values(item.value).every((campo: any) =>
              campo.is_deleted === true
            );

            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleNestedBlock = (entryIdx: number, arrayKey: string, blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const item = e[arrayKey]?.[blockIdx];
          if (item?.value) {
            const targetDeletedState = isTrash ? false : true;

            item.is_deleted = targetDeletedState;

            Object.values(item.value).forEach((campo: any) => {
              if (campo) {
                campo.is_deleted = targetDeletedState;
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleListTitle = (entryIdx: number, arrayKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (e: any, i?: number) => {
        if (i === entryIdx) {
          const arrayItems = e[arrayKey];
          if (arrayItems && Array.isArray(arrayItems)) {
            const targetDeletedState = isTrash ? false : true;

            arrayItems.forEach((item: any) => {
              item.is_deleted = targetDeletedState;
              if (item.value) {
                Object.values(item.value).forEach((campo: any) => {
                  if (campo) {
                    campo.is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: PossivelContato) => React.ReactElement | null
  > = {
    nome_completo: (entry) => {
      if (!entry?.nome_completo || !includeKey(entry.nome_completo.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={3} key={`nome-completo-${idx}`}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.nome_completo) {
                    e.nome_completo.is_deleted = !e.nome_completo.is_deleted;

                    const targetDeletedState = e.nome_completo.is_deleted;

                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    if (e.pessoa) {
                      e.pessoa.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.telefones) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.enderecos) {
                      e.enderecos.forEach((end: any) => {
                        end.is_deleted = targetDeletedState;
                        if (end.value) {
                          Object.values(end.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.nome_completo.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.nome_completo.value)}
              tooltip={renderSourceTooltip(entry.nome_completo.source)}
              className="border-0 pb-0"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    razao_social: (entry) => {
      if (!entry?.razao_social || !includeKey(entry.razao_social.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={3} key={`razao-social-${idx}`}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.razao_social) {
                    e.razao_social.is_deleted = !e.razao_social.is_deleted;

                    const targetDeletedState = e.razao_social.is_deleted;

                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    if (e.pessoa) {
                      e.pessoa.forEach((p: any) => {
                        p.is_deleted = targetDeletedState;
                        if (p.value) {
                          Object.values(p.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.telefones) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.enderecos) {
                      e.enderecos.forEach((end: any) => {
                        end.is_deleted = targetDeletedState;
                        if (end.value) {
                          Object.values(end.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.razao_social.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.razao_social.value)}
              tooltip={renderSourceTooltip(entry.razao_social.source)}
              className="border-0 pb-0"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(String((val as any).value))}
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    pessoa: (entry) => {
      if (!entry?.pessoa?.length || !shouldIncludeList(entry.pessoa)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.pessoa
        .map((p, i) => ({ bloco: p, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`pessoa-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'pessoa')}
          >
            <ReportsCustomLabel
              label="PESSOAS"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <GridItem key={`pessoa-${idx}-${origIdx}`} cols={1}>
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12"
                  onToggleField={() => onToggleNestedBlock(idx, 'pessoa', origIdx)}
                >
                  <ReportsCustomLabel
                    label={`PESSOA ${!isTrash ? blockRenderIdx + 1 : ""}`}
                    colorClass="bg-border"
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  />
                </CustomGridItem>
                <div className="pl-5">
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any) => (
                      <CustomGridItem
                        key={`pessoa-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'pessoa', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          icon={
                            <MdOutlineSubdirectoryArrowRight size={16} />
                          }
                          value={String(fieldValue.value || "")}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </div>
              </GridItem>
            ))}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    telefones: (entry) => {
      if (!entry?.telefones?.length || !shouldIncludeList(entry.telefones)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.telefones
        .map((t, i) => ({ bloco: t, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`telefones-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'telefones')}
          >
            <ReportsCustomLabel
              label="TELEFONES"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <GridItem key={`tel-${idx}-${origIdx}`} cols={1}>
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12"
                  onToggleField={() => onToggleNestedBlock(idx, 'telefones', origIdx)}
                >
                  <ReportsCustomLabel
                    label={`TELEFONE ${!isTrash ? blockRenderIdx + 1 : ""}`}
                    colorClass="bg-border"
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  />
                </CustomGridItem>
                <div className="pl-5">
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any) => (
                      <CustomGridItem
                        key={`tel-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'telefones', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          icon={
                            <MdOutlineSubdirectoryArrowRight size={16} />
                          }
                          value={String(fieldValue.value || "")}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </div>
              </GridItem>
            ))}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    enderecos: (entry) => {
      if (!entry?.enderecos?.length || !shouldIncludeList(entry.enderecos)) return null;
      const idx = idxMap.get(entry)!;

      const blocks = entry.enderecos
        .map((end, i) => ({ bloco: end, idx: i }))
        .filter(({ bloco }) => shouldIncludeNestedBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} key={`enderecos-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => onToggleListTitle(idx, 'enderecos')}
          >
            <ReportsCustomLabel
              label="ENDEREÇOS"
              colorClass="bg-primary"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
              <GridItem key={`end-${idx}-${origIdx}`} cols={1}>
                <CustomGridItem
                  cols={1}
                  containerClassName="w-fit pr-12"
                  onToggleField={() => onToggleNestedBlock(idx, 'enderecos', origIdx)}
                >
                  <ReportsCustomLabel
                    label={`ENDEREÇO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                    colorClass="bg-border"
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  />
                </CustomGridItem>
                <div className="pl-5">
                  {Object.entries(bloco.value)
                    .filter(([_, v]: any) =>
                      isTrash ? v.is_deleted : !v.is_deleted
                    )
                    .map(([fieldKey, fieldValue]: any) => (
                      <CustomGridItem
                        key={`end-${idx}-${origIdx}-${fieldKey}`}
                        cols={1}
                        className="py-1"
                        onToggleField={() => onToggleNestedField(idx, 'enderecos', origIdx, fieldKey)}
                      >
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(
                            fieldValue.label || fieldKey
                          ).toUpperCase()}
                          colorClass="bg-border"
                          icon={
                            <MdOutlineSubdirectoryArrowRight size={16} />
                          }
                          value={String(fieldValue.value || "")}
                          tooltip={renderSourceTooltip(fieldValue.source)}
                        />
                      </CustomGridItem>
                    ))}
                </div>
              </GridItem>
            ))}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof PossivelContato>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelContato): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelContato>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Possíveis Contatos] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof PossivelContato> = [
      'nome_completo',
      'razao_social',
      'detalhes',
      'pessoa',
      'telefones',
      'enderecos'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: PossivelContato[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Possíveis Contatos] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        return testNomeCompletoDeleted(entry) ||
          testRazaoSocialDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testPessoaDeleted(entry) ||
          testTelefonesDeleted(entry) ||
          testEnderecosDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          (entry.pessoa && entry.pessoa.some((p: any) =>
            Object.values(p.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          (entry.telefones && entry.telefones.some((t: any) =>
            Object.values(t.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          (entry.enderecos && entry.enderecos.some((end: any) =>
            Object.values(end.value || {}).some((v: any) => v.is_deleted === true)
          ));
      } else {
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`possivel-contato-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.nome_completo) {
          entry.nome_completo.is_deleted = targetDeletedState;
        }
        if (entry.razao_social) {
          entry.razao_social.is_deleted = targetDeletedState;
        }
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry.pessoa) {
          entry.pessoa.forEach((p: any) => {
            p.is_deleted = targetDeletedState;
            if (p.value) {
              Object.values(p.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if (entry.telefones) {
          entry.telefones.forEach((t: any) => {
            t.is_deleted = targetDeletedState;
            if (t.value) {
              Object.values(t.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
        if (entry.enderecos) {
          entry.enderecos.forEach((end: any) => {
            end.is_deleted = targetDeletedState;
            if (end.value) {
              Object.values(end.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}