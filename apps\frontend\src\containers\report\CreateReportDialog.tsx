import { Button, Input, Select, Text } from "@snap/design-system";
import { Check } from "lucide-react";
import { useEffect, useCallback, useMemo } from "react";
import { useParams } from "react-router";
import { ReportInput } from "~/components/ReportInput";
import { useUserData } from "~/store/userStore";
import {
  useNewReportActions,
  useNewReportInputValue,
  useNewReportSelectedType,
} from "~/store/newReportStore";
import {
  useActiveTab,
  useFolderName,
  useSelectedReports,
  useCreateFolderActions,
  useCanCreateFolder,
  useFolderError,
  useIsCreatingFolder,
} from "~/store/createFolderStore";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { toast } from "sonner";
import { useEncryption } from "~/hooks/useEncryption";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { Column } from "~/components/Table";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { NewReportResponse } from "~/types/global";
import { useModalControl } from "@snap/design-system";

export function CreateReportDialogContent() {
  const userData = useUserData();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const { setSelectedReportType, setReportInputValue } = useNewReportActions();
  const params = useParams<{ folderId?: string }>();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  const selectedReports = useSelectedReports();
  const folderError = useFolderError();
  const { setActiveTab, setFolderName, setParentFolderId } = useCreateFolderActions();
  const reportTypes = userData?.report_types;
  // Memoize options to prevent unnecessary re-renders
  const reportTypeOptions = useMemo(() =>
    reportTypes?.filter((type) => type !== "combinado")
      .map((type) => ({
        value: type,
        label: type.toUpperCase(),
      })) || [],
    [reportTypes]
  );
  const { reportListQuery } = useReportCRUD();
  const { data: reportDataResponse, isFetching } = reportListQuery;

  useEffect(() => {
    const firstType = reportTypes?.filter(type => type !== "combinado")[0] || "";
    setSelectedReportType(firstType);
  }, [userData?.report_types, setSelectedReportType]);

  // Set parent folder ID from URL params
  useEffect(() => {
    if (params.folderId) {
      setParentFolderId(params.folderId);
    } else {
      setParentFolderId(null);
    }
  }, [params.folderId, setParentFolderId]);

  // Memoize tab change handlers to prevent unnecessary re-renders
  const handleReportTabClick = useCallback(() => {
    setActiveTab("report");
  }, [setActiveTab]);

  const handleFolderTabClick = useCallback(() => {
    setActiveTab("folder");
  }, [setActiveTab]);

  const handleFolderNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFolderName(e.target.value);
  }, [setFolderName]);

  const columnProps = useMemo(() => ({
    report_name: REPORT_CONSTANTS.new_report.report_name,
    modified_at: REPORT_CONSTANTS.new_report.modified_at,
  }), []);

  const userColumns: Column[] = useMemo(() => [
    {
      key: columnProps.report_name,
      header: "Selecione os relatórios",
      widthClass: "w-2/5 min-w-[200px]",
      className: "overflow-hidden",
      render: (_, row: NewReportResponse) => (
        <div className="flex items-center gap-4">
          <div className="truncate">
            <Text variant="body-md" className="font-semibold">{row?.report_name as string}</Text>
          </div>
        </div>
      ),
    },
    {
      key: columnProps.modified_at,
      header: "Modificado Em",
      widthClass: "w-1/5 min-w-[120px]",
      render: (_, row: NewReportResponse) => (
        <div className="flex items-center gap-4">
          <Text variant="body-md" className="font-semibold">{row.modified_at as string}</Text>
        </div>
      ),
    },
  ], [columnProps]);

  return (
    <div className="space-y-4 min-h-[200px]">
      <p className="text-md mb-4 font-semibold">
        Que tipo de item você deseja criar?
      </p>
      <div className="flex items-center">
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "report" && "!bg-foreground !text-background"}`}
          onClick={handleReportTabClick}
          data-testid="button-create-report"
        >
          Criar Relatório
        </Button>
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "folder" && "!bg-foreground !text-background"}`}
          onClick={handleFolderTabClick}
          data-testid="button-create-folder"
        >
          Criar Pasta
        </Button>
      </div>
      <div className="mb-12">
        {activeTab === "report" && (
          <div className="grid grid-cols-2 gap-4 mb-2">
            <div>
              <Text className="block mb-1">Tipo de entrada:</Text>
              {userData?.report_types?.length ? (
                <Select
                  options={reportTypeOptions}
                  value={selectedReportType}
                  onChange={setSelectedReportType}
                  placeholder="Selecionar tipo"
                  data-testid="select-report-type"
                  className="w-full"
                />
              ) : (
                <p className="text-sm text-accent">Nenhum tipo de relatório disponível</p>
              )}
            </div>
            <div>
              <Text className="block mb-1">Preencha o campo:</Text>
              <div className="pt-1.5">
                <ReportInput
                  inputValue={reportInputValue}
                  setInputValue={setReportInputValue}
                  reportType={selectedReportType}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === "folder" && (
          <div className="flex flex-col gap-4">
            <div>
              <Text className="block mb-1">Digite o nome da pasta que deseja criar:</Text>
              <Input
                type="text"
                variant="outlined"
                value={folderName}
                placeholder="Nome da pasta"
                onChange={handleFolderNameChange}
                className={`rounded-none border-0 w-full border-b-1 border-dashed pl-0 text-[16px] uppercase ${folderError ? 'border-accent' : ''}`}
              />
              {folderError && (
                <Text variant="body-sm" className="text-accent mt-1">
                  {folderError}
                </Text>
              )}
            </div>
            {/* TODO: descomentar quando implementar a seleção de relatórios
            <DataTable
              columns={userColumns}
              data={reportDataResponse?.data || []}
              keyField={REPORT_CONSTANTS.new_report.report_id as keyof NewReportResponse}
              enableSelection
            /> */}
          </div>
        )}
      </div>
    </div>
  );
}

export function CreateReportDialogFooter() {
  const { newReportMutation } = useReportCRUD();
  const { handleCreateFolder } = useFolderCRUD();
  const { encryptData } = useEncryption();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const { onClose } = useModalControl();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  const selectedReports = useSelectedReports();
  const canCreateFolder = useCanCreateFolder();
  const isCreatingFolder = useIsCreatingFolder();

  const handleCreateNewReport = async () => {
    if (!selectedReportType || !reportInputValue) {
      toast("Preencha todos os campos", {
        description: "Necessário tipo de entrada e seu valor",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
      return;
    }

    if (selectedReportType === REPORT_CONSTANTS.types.email && !reportInputValue.includes("@")) {
      toast("Erro", {
        description: "Email inválido",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
      return;
    }

    const searchArgsFormatInput = {
      [selectedReportType]: [reportInputValue],
    };
    const encryptedValue = await encryptData(searchArgsFormatInput);

    if (!encryptedValue.data) {
      toast("Erro", {
        description: "Erro ao criptografar dados",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
      return;
    }

    newReportMutation.mutateAsync({
      report_type: selectedReportType,
      report_input_value: reportInputValue,
      report_input_encrypted: encryptedValue.data,
    });
  };

  return (
    <div className="flex gap-4">
      {activeTab === "report" && (
        <Button
          className="uppercase"
          onClick={handleCreateNewReport}
          data-testid="button-confirm-create-report"
          disabled={newReportMutation.isPending || !selectedReportType || !reportInputValue}
          iconPosition="right"
          icon={newReportMutation.isPending ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          Criar Relatório
        </Button>
      )}

      {activeTab === "folder" && (
        <Button
          className="uppercase"
          onClick={() => handleCreateFolder(folderName, selectedReports)}
          data-testid="button-confirm-create-folder"
          disabled={!canCreateFolder || isCreatingFolder}
          iconPosition="right"
          icon={isCreatingFolder ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          {isCreatingFolder ? "Criando..." : "Criar Pasta"}
        </Button>
      )}

      <Button
        onClick={onClose}
        data-testid="button-cancel-create-report"
      >
        Cancelar
      </Button>
    </div>
  );
}

// Export as static properties so you can use the composition pattern
export const CreateReportDialog = {
  Content: CreateReportDialogContent,
  Footer: CreateReportDialogFooter,
};
