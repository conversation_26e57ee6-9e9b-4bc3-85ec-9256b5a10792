"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table"
import { Checkbox } from "~/components/ui/checkbox"
import { useState } from "react"
import { Pagination } from "./Pagination"
import EmptyData from "./EmptyData"
import type { ReactNode } from "react"
import { cn } from "~/lib/utils"
import { Text, Button } from "@snap/design-system"

export interface Column<T = any> {
  key: string
  header?: string
  render?: (value: any, row: T, index: number) => ReactNode
  className?: string
  hidden?: boolean
  widthClass?: string
}

export interface ActionButton<T = any> {
  icon: ReactNode
  onClick: (row: T) => void
  label?: string
  className?: string
}

export interface DataTableProps<T = any> {
  columns: Column<T>[]
  data: T[]
  className?: string
  showHeaders?: boolean
  actionsTitle?: string
  actions?: ActionButton<T>[]
  actionsWidthClass?: string
  enableSelection?: boolean
  onSelectionChange?: (selectedRows: T[]) => void
  keyField?: keyof T
  useFixedLayout?: boolean
  pagination?: {
    pageSize: number
    totalItems?: number
    totalPages?: number
    currentPage?: number
    onPageChange?: (page: number) => void
  }
}

const columnColors = ["bg-table-cell", "bg-table-cell-alt"]
const headerColors = ["bg-table-header", "bg-table-header-alt"]

export function DataTable<T extends Record<string, any> = any>({
  columns,
  data,
  className = "",
  showHeaders = true,
  actionsTitle = "Ações",
  actions = [],
  actionsWidthClass = "w-24",
  enableSelection = false,
  onSelectionChange,
  keyField = "id" as keyof T,
  useFixedLayout = false,
  pagination,
}: DataTableProps<T>) {
  const [selectedRows, setSelectedRows] = useState<Record<string | number, boolean>>({})
  const currentPage = pagination?.currentPage || 1
  const pageSize = pagination?.pageSize || data.length
  const totalPages = pagination?.totalPages || (pagination?.totalItems ? Math.ceil(pagination.totalItems / pageSize) : 1)
  const paginatedData = data
  const shouldShowPagination = pagination && pagination.totalItems && pagination.totalItems > pageSize && totalPages > 1

  const handleSelectRow = (row: T, isSelected: boolean) => {
    const rowKey = String(row[keyField] || "")
    const newSelectedRows = { ...selectedRows, [rowKey]: isSelected }
    setSelectedRows(newSelectedRows)

    if (onSelectionChange) {
      const selectedItems = data.filter((item) => {
        const itemKey = String(item[keyField] || "")
        return newSelectedRows[itemKey]
      })
      onSelectionChange(selectedItems)
    }
  }

  const handleSelectAll = (isSelected: boolean) => {
    const newSelectedRows: Record<string | number, boolean> = {}
    paginatedData.forEach((row) => {
      const rowKey = String(row[keyField] || "")
      newSelectedRows[rowKey] = isSelected
    })
    setSelectedRows(newSelectedRows)

    if (onSelectionChange) {
      onSelectionChange(isSelected ? [...paginatedData] : [])
    }
  }

  const isRowSelected = (row: T) => {
    const rowKey = String(row[keyField] || "")
    return !!selectedRows[rowKey]
  }

  const areAllRowsSelected = () => {
    return paginatedData.length > 0 && paginatedData.every((row) => isRowSelected(row))
  }

  const handlePageChange = (page: number) => {
    if (pagination?.onPageChange) {
      pagination.onPageChange(page)
    }
  }

  const visibleColumns = columns.filter((col) => !col.hidden)

  return (
    <div className="space-y-4">
      <div className={`w-full border border-table-border overflow-hidden ${className}`}>
        <Table className={useFixedLayout ? "table-fixed" : ""}>
          {showHeaders && (
            <TableHeader>
              <TableRow className="border-table-border hover:bg-table-hover">
                {enableSelection && (
                  <TableHead className={cn("w-12", headerColors[0])}>
                    <Checkbox checked={areAllRowsSelected()} onCheckedChange={handleSelectAll} className="ml-3" />
                  </TableHead>
                )}

                {visibleColumns?.map(
                  (column, index) =>
                    column.header && (
                      <TableHead
                        key={column.key}
                        className={cn(
                          "p-3",
                          headerColors[index % headerColors.length],
                          column.className,
                          column.widthClass
                        )}
                      >
                        <Text variant="body-lg" className="font-semibold">{column.header}</Text>
                      </TableHead>
                    ),
                )}

                {actions.length > 0 && (
                  <TableHead
                    className={cn("text-center", headerColors[visibleColumns.length % headerColors.length], actionsWidthClass)}
                  ><Text variant="body-lg" className="font-semibold">{actionsTitle}</Text></TableHead>
                )}
              </TableRow>
            </TableHeader>
          )}

          <TableBody>
            {paginatedData?.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={visibleColumns.length + (enableSelection ? 1 : 0) + (actions.length > 0 ? 1 : 0)}
                  className="p-0"
                >
                  <EmptyData />
                </TableCell>
              </TableRow>
            ) : (
              paginatedData?.map((row, rowIndex) => (
                <TableRow key={String(row[keyField]) || rowIndex} className="border-table-border hover:bg-table-hover">
                  {enableSelection && (
                    <TableCell className={cn("w-12", columnColors[0])}>
                      <Checkbox
                        checked={isRowSelected(row)}
                        onCheckedChange={(checked) => handleSelectRow(row, !!checked)}
                        className="ml-3 cursor-pointer"
                      />
                    </TableCell>
                  )}

                  {visibleColumns?.map((column, colIndex) => (
                    <TableCell
                      key={`${rowIndex}-${column.key}`}
                      className={cn(
                        "py-2 px-3",
                        columnColors[colIndex % columnColors.length],
                        column.className,
                        column.widthClass
                      )}
                    >
                      {column.render ? column.render(row[column.key as keyof T], row, rowIndex) : row[column.key as keyof T]}
                    </TableCell>
                  ))}

                  {actions?.length > 0 && (
                    <TableCell className={cn(actionsWidthClass, columnColors[visibleColumns.length % columnColors.length])}>
                      <div className="flex justify-end">
                        {actions.map((action, actionIndex) => {
                          return (
                            <div key={`action-${rowIndex}-${actionIndex}`} className="flex items-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => action.onClick(row)}
                                className={cn("hover:opacity-80", action.className)}
                                 title={action.label}
                              >
                                {action.icon}

                              </Button>
                              {
                                actionIndex !== actions.length - 1 && (
                                  <div className="w-px h-4 mx-2 my-auto border-l border-dotted border-foreground" />
                                )
                              }
                            </div>
                          )
                        })}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {shouldShowPagination && paginatedData?.length > 0 && <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />}
    </div>
  )
}
