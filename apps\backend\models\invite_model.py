from enum import Enum as PyEnum
from sqlalchemy import Column, String, DateTime, ForeignKey, Enum as SAEnum, Integer
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import relationship

from models.base import Base
from core.constants import TableNames, InviteStatusCons, InviteTypeCons

class InviteStatus(str, PyEnum):
    ENVIADO = InviteStatusCons.enviado
    NEGADO =  InviteStatusCons.negado
    ACEITO =  InviteStatusCons.aceito
    CANCELADO =  InviteStatusCons.cancelado
    DESVINCULADO =  InviteStatusCons.desvinculado

class InviteType(str, PyEnum):
    INVESTIGADOR = InviteTypeCons.investigador
    ADMINISTRADOR = InviteTypeCons.administrador

class Invite(Base):
    __tablename__ = TableNames.invite
    __table_args__ = {"schema": "public"}

    invite_id = Column(PGUUID, primary_key=True, server_default='gen_random_uuid()')
    user_sender_id = Column(PGUUID, ForeignKey('public.users.user_id'), nullable=False)
    organization_id = Column(PGUUID, ForeignKey('public.organizations.organization_id'), nullable=False)
    status_invite = Column(
        SAEnum(
            InviteStatus,
            name="invite_status",
            create_type=False,
            values_callable=lambda obj: [e.value for e in obj]
        ),
        nullable=False
    )
    type_invite = Column(
        SAEnum(
            InviteType,
            name="invite_type",
            create_type=False,
            values_callable=lambda obj: [e.value for e in obj]
        ),
        nullable=False
    )
    report_types = Column(JSONB, nullable=False)
    sent_at = Column(DateTime(timezone=True), nullable=False)
    email_invited = Column(String, nullable=False)
    # invite_valid_until = Column(DateTime(timezone=True), nullable=False)
    credits_sent = Column(Integer, nullable=False)

    user_sender = relationship("Users", back_populates="sent_invites")
    organization = relationship("Organizations", back_populates="invites")

    def __repr__(self):
        return f"<Invite(invite_id={self.invite_id}, email_invited={self.email_invited})>"
