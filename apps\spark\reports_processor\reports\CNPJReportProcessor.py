from typing import Dict, Any

from .BaseReportProcessor import BaseReportProcessor, ReportTypeConfig
from ..constants import ReportKeys
from ..FormatFrontFormat import VinculoSection
from ..EntityExtractor import VinculoConfig, ExtractionResult
from ..CallbackProcessors import CallbackProcessors


class CNPJReportProcessor(BaseReportProcessor):
    """Processor for CNPJ reports"""

    def get_config(self) -> ReportTypeConfig:
        return ReportTypeConfig(
            do_filter_doc='CNPJ',
            do_filter_name='RazaoSocial',
            title_format='CNPJ',
            enabled_sections=[
                VinculoSection.PHONES, VinculoSection.EMAILS, VinculoSection.ENDERECOS,
                VinculoSection.SOCIEDADES, VinculoSection.SOCIOS, VinculoSection.JUNTAS_COMERCIAIS,
                VinculoSection.PROCESSOS, VinculoSection.DIARIOS_OFICIAIS_DOC,
                VinculoSection.DIARIOS_OFICIAIS_NOME, VinculoSection.RECURSOS_RECEBIDOS,
                VinculoSection.DOACOES_ELEITORAIS_ENVIADAS, VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS,
                VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS, VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS
            ]
        )

    def _get_custom_societario(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        VinculoSection, VinculoConfig]:
        # Extract societario data for CNPJ (both pessoas and empresas)
        societario_p = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.PESSOA, filter_base_data_callback=CallbackProcessors.filter_for_sociedades_vinculos)
        )

        societario_e = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.EMPRESA,
                          filter_base_data_callback=CallbackProcessors.filter_for_sociedades_vinculos)
        )

        # Combine the data
        # TODO: incluir sociedades depois de corrigir CNPJ
        combined_societario_data = {
            'socios_p': societario_p.data,
            'socios_e': societario_e.data
        }
        combined_sources = set(societario_p.sources + societario_e.sources)

        return {
            VinculoSection.SOCIEDADES: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIEDADES),
                extract_func=lambda *args, **kwargs: ExtractionResult(combined_societario_data.get('sociedades', []), combined_sources),
                extract_type=ReportKeys.EMPRESA
            ),
            VinculoSection.SOCIOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SOCIOS),
                extract_func=lambda *args, **kwargs: ExtractionResult(combined_societario_data['socios_p'] + combined_societario_data['socios_e'],
                                         combined_sources),
                extract_type=ReportKeys.EMPRESA
            )
        }

    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        Any, VinculoConfig]:

        custom_dict = self._get_custom_societario(other_result_data, entity_type, search_value)
        custom_dict.update(self._get_custom_processos(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_fornecimentos_campanha(other_result_data, entity_type, search_value))
        custom_dict.update(self._get_custom_doacoes_campanha(other_result_data, entity_type, search_value))

        return custom_dict

    def adjust_metadata(self, processed_result, metadata):
        self._adjust_metadata_age(processed_result, metadata)