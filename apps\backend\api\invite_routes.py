import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter, Depends, HTTPException, status, Query, <PERSON>ie, Response
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from datetime import datetime


from database.db import get_db

from services.user_service import (UserStandaloneService, InvestigadorService, AdministradorService)
from services.auth_service import auth_guard
from services.invite_service import InviteService
from services.organization_users_service import OrganizationUsersService
from services.organization_service import OrganizationService
from services.credits_service import CreditsService
from services.apikey_service import ApikeyService

from schemas.user_schema import Investigator<PERSON>ser, AdministratorUser
from schemas.invite_schema import InviteAnswer, InviteCreate

from core.jwt_utils import logout_user_using_token
from core.constants import Roles, JWTFields, DefaultPageInvites, Fields, InviteFields, UserFields

from models.invite_model import InviteStatus, InviteType

from utils.jwt_utils import JWTUtils
from exceptions.business_exceptions import (
    InvitePermissionDeniedError,
    InvalidInviteTypeError,
    InviteNotFoundError,
    UserNoActiveOrganizationError,
    InviteInternalError
)


router = APIRouter()


@router.post("/create_invite")
async def create_invite(invite_info: InviteCreate,
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard)):

    logger.info("[InviteEndpoint] Starting invite creation process")

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    logger.debug("[InviteEndpoint] User roles: %s", user_roles)

    if Roles.create_invite_user not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have create invite role", user_id)
        raise InvitePermissionDeniedError("criar convite")
    try:
        logger.info("[InviteEndpoint] Creating invite in database for email: %s", invite_info.email_invited)
        logger.debug("[InviteEndpoint] Invite details - type: %s, report_types: %s", 
                    invite_info.type_invite, 
                    invite_info.report_types)

        invite_service = InviteService(db=db, user_id=user_id)

        invite_id = await invite_service.create_invite(invite=invite_info)

        logger.info(
            "[InviteEndpoint] Invite created successfully with ID: %s for email: %s",
            invite_id,
            invite_info.email_invited
        )

        return invite_id
        

    except HTTPException as he:
        logger.error(
            "[InviteEndpoint] HTTP error during invite creation: %s",
            str(he),
            exc_info=True
        )
        raise InviteInternalError("criar", str(he.detail))
    except Exception as e:
        logger.error(
            "[InviteEndpoint] Unexpected error during invite creation: %s",
            str(e),
            exc_info=True
        )
        raise InviteInternalError("criar", str(e))
    

@router.post("/answer_invite")
async def answer_invite(invite_answer: InviteAnswer,
                        response: Response,
                        refresh_token: str = Cookie(None),                        
                        db: AsyncSession = Depends(get_db),
                        user = Depends(auth_guard)):
    
    logger.info("[InviteEndpoint] Processing invite answer from user: %s", user.get(JWTFields.email))
    logger.debug("[InviteEndpoint] Invite answer details - accept: %s, invite_id: %s, type: %s",
                invite_answer.accept_invite,
                invite_answer.invite_id)


    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_email = token_decoded.get_user_email()
    user_roles = token_decoded.get_user_roles()

    if Roles.answer_invite not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have answer invite role", user_id)
        raise InvitePermissionDeniedError("responder convite")

    invited = InviteService(db = db, user_id = user_id)
    
    if invite_answer.accept_invite:
        logger.info("[InviteEndpoint] User %s accepting invite for invite_id %s", 
                   user_email, 
                   invite_answer.invite_id)
        

        organization_users_service = OrganizationUsersService(db=db, user_id=user_id)

        user_organization_data = await organization_users_service.get_organization_user()
        if user_organization_data:
            #if user is already active in an organization, change it to status inativo and exited_at to now
            await organization_users_service.update_organization_user_status_to_inativo()
            logger.warning("[InviteEndpoint] Changed status of previous organization to inactive for user: %s", user_id)

        
        #set the invite status to aceito: return the organization id

        invite_details = await invited.update_invite_status(email_invited=user_email,
                                           invite_id=invite_answer.invite_id,
                                           new_status=InviteStatus.ACEITO)
        
        logger.debug("[InviteEndpoint] Updated invite status to accepted")

        type_invite = invite_details.get(InviteFields.type_invite)
        organization_id = invite_details.get(InviteFields.organization_id)

        api_key = None

        organization_service = OrganizationService(db=db, organization_id=organization_id)
        org_data = await organization_service.get_organization_data()
        api_key = org_data.api_key

        # Update user role based on invite type
        if type_invite == InviteType.INVESTIGADOR:
            logger.info("[InviteEndpoint] Updating user %s to investigator role", user_id)
            investigator_data = InvestigatorUser(report_types=invite_details.get(InviteFields.report_types))
            investigador = InvestigadorService(db=db, user_id=user_id)

            await investigador.update_user(user_data=investigator_data, credits_monthly=invite_details.get(InviteFields.credits_sent), api_key=api_key)

        elif type_invite == InviteType.ADMINISTRADOR:
            logger.info("[InviteEndpoint] Updating user %s to manager role", user_id)
            administrador_data = AdministratorUser(report_types=invite_details.get(InviteFields.report_types))
            administrador = AdministradorService(db=db, user_id=user_id)
            await administrador.update_user(user_data = administrador_data, credits_monthly=invite_details.get(InviteFields.credits_sent), api_key=api_key)  

        else:
            logger.error("[InviteEndpoint] Invalid invite type: %s", type_invite)
            raise InvalidInviteTypeError(type_invite)

        logger.info("[InviteEndpoint] Associating user %s with invite %s", 
                   user_id, 
                   invite_answer.invite_id)
        # Associate user with organization

        organization_users_id = await organization_users_service.insert_organization_users_into_local_db(organization_id=invite_details.get(InviteFields.organization_id))
        
        logger.info("[InviteEndpoint] Successfully completed invite acceptance process")

        await logout_user_using_token(refresh_token=refresh_token, response=response)

        logger.info("[InviteEndpoint] User %s logout user.", user_id)

        return {"message": "Invite accepted successfully", "organization_users_id": organization_users_id}
    
    else:
        logger.info("[InviteEndpoint] User %s declining invite for invite %s", 
                   user_email, 
                   invite_answer.invite_id)
        
        await invited.update_invite_status(email_invited=user_email,
                                           invite_id=invite_answer.invite_id,
                                           new_status=InviteStatus.NEGADO)
        logger.info("[InviteEndpoint] Successfully updated invite status to declined")
        return {"message": "Invite declined"}


@router.put("/cancel_invite/{invite_id}")
async def cancel_invite(invite_id: str,
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard)):

    logger.info("[InviteEndpoint] Starting invite cancellation process")

    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.cancel_invite not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have cancel invite role", user_id)
        raise InvitePermissionDeniedError("cancelar convite")

    invite_service = InviteService(db=db, user_id=user_id)

    try:
        await invite_service.update_invite_status(new_status=InviteStatus.CANCELADO, invite_id=invite_id)
        logger.info("[InviteEndpoint] Successfully cancelled invite %s", invite_id)
        return {"message": "Invite cancelled successfully"}
    except HTTPException as he:
        logger.error(
            "[InviteEndpoint] HTTP error during invite cancellation: %s",
            str(he),
            exc_info=True,
        )
        raise InviteInternalError("cancelar", str(he.detail))
    except Exception as e:
        logger.error(
            "[InviteEndpoint] Unexpected error during invite cancellation: %s",
            str(e),
            exc_info=True,
        )
        raise InviteInternalError("cancelar", str(e))


@router.get("/get_organization_invite")
async def get_organization_invite(
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard),
    status_invite: str = Query(None),
    type_invite: str = Query(None),
    specific_date: datetime = Query(None),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("sent_at", description="Column to order by"),
    limit: int = Query(DefaultPageInvites.pagedefault),
    page: int = Query(1)
):
    
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_email = token_decoded.get_user_email()
    user_roles = token_decoded.get_user_roles()

    if Roles.get_organization_invite not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have get organization invite role", user_id)
        raise InvitePermissionDeniedError("obter convites da organização")
    
    logger.info("[InviteEndpoint] Retrieving invites for user: %s", user_email)

    logger.info("[InviteEndpoint] User %s is attempting to retrieve organization invites", user_id)
    organization_service = OrganizationUsersService(db=db, user_id=user_id)

    def organization_user_to_dict(org_user):
        if org_user is None:
            return None
        return {
            "organization_users_id": str(org_user.organization_users_id),
            "organization_id": str(org_user.organization_id),
            "user_id": str(org_user.user_id),
            "status": str(org_user.status),
            "joined_at": org_user.joined_at.isoformat() if org_user.joined_at else None,
            "exited_at": org_user.exited_at.isoformat() if org_user.exited_at else None,
            "validate_until": org_user.validate_until.isoformat() if org_user.validate_until else None
        }

    logger.info("[InviteEndpoint] Fetching organization user data for user_id: %s", user_id)
    user_organization_data = await organization_service.get_organization_user()

    user_organization_dict = organization_user_to_dict(user_organization_data)

    if user_organization_dict is None:
        logger.info("[InviteEndpoint] No organization found for user_id: %s. Returning empty list.", user_id)
        return []

    organization_id = user_organization_dict["organization_id"]
    logger.info("[InviteEndpoint] Found organization_id: %s for user_id: %s", organization_id, user_id)

    invited = InviteService(db=db, user_id=user_id)
    invited.set_organization_id(organization_id=organization_id)
    logger.info("[InviteEndpoint] Fetching invites for organization_id: %s with filters - status_invite: %s, type_invite: %s, specific_date: %s, order: %s, column_order: %s, limit: %d, page: %d", organization_id, status_invite, type_invite, specific_date, order, column_order, limit, page)
    invites = await invited.get_organizations_invite(
        status_invite=status_invite,
        type_invite=type_invite,
        specific_date=specific_date,
        order=order,
        column_order=column_order,
        limit=limit,
        page=page
    )

    logger.info("[InviteEndpoint] Found %d invites for organization %s", 
                len(invites.data) if invites and hasattr(invites, 'data') else 0, organization_id)
    logger.debug("[InviteEndpoint] Invite details: %s", invites)

    return invites


@router.get("/get_user_invite")
async def get_user_invite(
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard),
    status_invite: Optional[str] = Query(None),
    type_invite: Optional[str] = Query(None)
):
    
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_email = token_decoded.get_user_email()
    user_roles = token_decoded.get_user_roles()

    if Roles.get_user_invite not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have get user invite role", user_id)
        raise InvitePermissionDeniedError("obter convites do usuário")

    invited = InviteService(db=db, user_id=user_id)
    invites = await invited.get_user_invite(
        email=user_email,
        status_invite=status_invite,
        type_invite=type_invite
    )

    logger.info("[InviteEndpoint] Found %d invites for email %s", 
                len(invites) if invites else 0, user_email)
    logger.debug("[InviteEndpoint] Invite details: %s", invites)

    return invites


@router.get("/get_invite_details/{invite_id}")
async def get_invite_details(invite_id: str,
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard)):

    logger.info("[get_invite_details] Called with invite_id: %s", invite_id)
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.get_invite_details not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have get invite details role", user_id)
        raise InvitePermissionDeniedError("obter detalhes do convite")

    invite_service = InviteService(db=db, user_id=user_id)
    invite_data_result = await invite_service.get_user_invite(invite_id=invite_id)

    logger.info("[get_invite_details] User has administrador role.")

    return invite_data_result


@router.get("/get_user_data_from_invite/{invite_id}")
async def get_user_data_from_invite_endpoint(invite_id: str,
    db: AsyncSession = Depends(get_db),
    user = Depends(auth_guard)):

    logger.info("[get_user_data_from_invite_endpoint] Called with invite_id: %s", invite_id)
    token_decoded = JWTUtils(user_jwt=user)

    user_id = token_decoded.get_user_id()
    user_roles = token_decoded.get_user_roles()

    if Roles.get_data_from_user_invite not in user_roles:
        logger.warning("[InviteEndpoint] Access denied - User %s does not have get data from user invite role", user_id)
        raise InvitePermissionDeniedError("obter dados do usuário do convite")
    logger.debug("[get_user_data_from_invite_endpoint] user_id: %s, user_roles: %s", user_id, user_roles)

    invite_service = InviteService(db=db, user_id=user_id)
    invite_data_result = await invite_service.get_user_invite(invite_id=invite_id)

    if len(invite_data_result) == 0:
        logger.warning("[get_user_data_from_invite_endpoint] No invite found for invite_id: %s", invite_id)
        raise InviteNotFoundError(invite_id)
    
    invite_searched = invite_data_result[0]


    email_invited = invite_searched.get(Fields.email_invited)
    user_service = UserStandaloneService(db=db, user_id=user_id)

    user_data_invited = await user_service.get_user_data(email=email_invited)
    logger.debug("[get_user_data_from_invite_endpoint] user_data_invited: %s", user_data_invited)
    
    user_updating_id = user_data_invited.user_id
    logger.debug("[get_user_data_from_invite_endpoint] user_updating_id: %s", user_updating_id)

    api_key_service = ApikeyService(db=db, user_id=user_id)
    api_key = await api_key_service.get_api_key()
    logger.debug("[get_user_data_from_invite_endpoint] api_key: %s", api_key)

    credits_service = CreditsService(db=db, user_id=user_updating_id)
    credits_service.set_api_key(api_key=api_key)

    user_data_dict = {Fields.total_credits: user_data_invited.credits,
                      UserFields.credits_monthly: user_data_invited.credits_monthly,
                      UserFields.next_reset_credits: user_data_invited.next_reset_credits,
                      Fields.report_types: user_data_invited.report_types,
                      Fields.role: user_data_invited.role,
                      Fields.name: user_data_invited.name,
                      Fields.user_id: user_updating_id,
                      Fields.email: email_invited
                      }
    
    minimun_credits, _ = await credits_service.compare_user_credits_with_api_credits(user_data_dict)
    logger.debug("[get_user_data_from_invite_endpoint] minimun_credits: %s", minimun_credits)
    user_data_dict["remaining_credits"] = minimun_credits

    organization_user_service = OrganizationUsersService(db=db, user_id=user_updating_id)
    user_organization_data = await organization_user_service.get_organization_user()
    logger.debug("[get_user_data_from_invite_endpoint] user_organization_data: %s", user_organization_data)

    if not user_organization_data:
        logger.warning(f"[InviteService] No active organization found for user: {user_id}")
        raise UserNoActiveOrganizationError(user_id)

    organization_id = user_organization_data.organization_id
    logger.debug("[get_user_data_from_invite_endpoint] organization_id: %s, user_organization_data.organization_id: %s", organization_id, user_organization_data.organization_id)
    
    if organization_id==user_organization_data.organization_id:
        logger.info("[get_user_data_from_invite_endpoint] User is in the same organization as the invite. Returning user data.")
        #check if the user is on the same organization as the invite
        return user_data_dict
    else:
        logger.warning("[get_user_data_from_invite_endpoint] User organization mismatch. Not returning user data.")
