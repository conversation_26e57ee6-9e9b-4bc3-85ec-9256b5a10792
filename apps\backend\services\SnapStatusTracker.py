import asyncio
import json
import logging
import random
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any

import httpx
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, WebSocketDisconnect, FastAPI
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_database_async_engine

from core.constants import (ReportStatus,
                            Timeouts, Endpoints, SummaryReportStatus, ReportMockValidator)

from schemas.report_schema import SnapApiResponse

from services.minio_service import save_to_minio
from services.websocket import try_send_websocket
from exceptions.business_exceptions import ReportNotImplementedError

logger = logging.getLogger(__name__)


class SnapStatusTracker:
    """Handles SNAP status tracking and report processing."""

    def __init__(self, app: FastAPI, user_id: str, reports_id: str, api_key: str):
        self.app = app
        self.user_id = user_id
        self.reports_id = reports_id
        self.connection_manager = app.state.connection_manager
        self.headers = {
            'Ocp-Apim-Subscription-Key': api_key,
            'Accept': 'application/json'
        }
        self.timeout_time = time.time() + Timeouts.TIMEOUT_SECONDS

    async def track_status(self, request_id: str, report_type: str, report_number: str,
                           report_search_args: Dict[str, Any]) -> None:
        """Main method to track SNAP status and process reports."""
        logger.info(
            "[snap_status_ws][user(%s)] Tracking SNAP status for request_id=%s, report_type=%s",
            self.user_id, request_id, report_type
        )

        try:
            # Get the final result from either mock or API
            final_result_json = await self._get_final_result(request_id, report_type, report_search_args)
            logger.info("[track_status][user(%s)] Final Result JSON to request_id %s is complete", self.user_id, request_id)
            # logica nao utilizada, mas uma possibilidade de uso
            # # Save raw data to MinIO
            # await self._save_raw_data(final_result_json)

            # Handle error cases with delay
            if self._is_error_case(report_type, report_search_args):
                await asyncio.sleep(60)

            # Process and save final report
            await self._process_and_save_final_report(
                final_result_json, report_type, report_number, report_search_args
            )

        except WebSocketDisconnect:
            logger.exception(
                "[snap_status_ws][user(%s)] WebSocket disconnected for request_id=%s. Skipping error handling.",
                self.user_id, request_id
            )
        except Exception as e:
            logger.exception(
                "[snap_status_ws][user(%s)] Unexpected error for request_id=%s: %s",
                self.user_id, request_id, e
            )

            await self._handle_error(request_id)

    async def _get_final_result(self, request_id: str, report_type: str,
                                report_search_args: Dict[str, Any]) -> Dict[str, Any]:
        """Get final result from either mock data or API."""
        if self._is_test_case(report_type, report_search_args):
            return await self._get_mock_result(report_type, report_search_args)
        else:
            return await self._get_api_result(request_id)

    async def _get_mock_result(self, report_type: str, report_search_args: Dict[str, Any]) -> Dict[str, Any]:
        """Load mock data for testing."""
        cpf = report_search_args[report_type]
        logger.info("[snap_status_ws][user(%s)] Mocking response for CPF: %s", self.user_id, cpf)

        if self._is_error_case(report_type, report_search_args):
            logger.info("[snap_status_ws][user(%s)] Mocking error response for CPF: %s", self.user_id, cpf)
            return {}

        file_path = Path(f"mocks/{report_type}") / f"{cpf}.json"
        logger.info("[snap_status_ws][user(%s)] Attempting to load mock file: %s", self.user_id, file_path)

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                result = json.load(f)
                logger.info("[snap_status_ws][user(%s)] Successfully loaded mock data from: %s", self.user_id,
                            file_path)
                return result
        except FileNotFoundError:
            logger.warning("[snap_status_ws][user(%s)] Mock file not found: %s", self.user_id, file_path)
            return {}

    async def _get_api_result(self, request_id: str) -> Dict[str, Any]:
        """Get result from API with polling and retries."""
        async with httpx.AsyncClient() as client:
            # Poll for status completion
            await self._poll_until_complete(client, request_id)

            # Fetch final result with retries
            return await self._fetch_final_result_with_retries(client, request_id)

    async def _poll_until_complete(self, client: httpx.AsyncClient, request_id: str) -> None:
        """Poll the API until the request is complete or timeout."""
        request_payload = {"id": request_id}
        status_code = ReportStatus.InProgress.pending

        logger.info("[snap_status_ws][user(%s)] Entered polling loop for request_id=%s", self.user_id, request_id)

        while status_code not in [ReportStatus.Success.success]:
            if time.time() > self.timeout_time:
                logger.warning("[snap_status_ws][user(%s)] Timeout reached for request_id=%s", self.user_id, request_id)
                await self._handle_timeout(request_id)
                break

            # Random sleep to avoid overwhelming the API
            sleep_time = random.randint(5, 20)
            logger.info("[snap_status_ws][user(%s)] Sleeping for %s seconds before next status check", self.user_id,
                        sleep_time)
            await asyncio.sleep(sleep_time)

            try:
                response = await client.post(
                    Endpoints.status_endpoint,
                    headers=self.headers,
                    json=request_payload
                )
                status_code = response.text
                logger.info("[snap_status_ws][user(%s)] Polled status for request_id=%s: %s", self.user_id, request_id,
                            status_code)
            except Exception as e:
                logger.error("[snap_status_ws][user(%s)] Error polling status for request_id=%s: %s", self.user_id,
                             request_id, e)
                continue

    async def _fetch_final_result_with_retries(self, client: httpx.AsyncClient, request_id: str) -> Dict[str, Any]:
        """Fetch final result with retry logic."""
        request_payload = {"id": request_id}
        max_retries = 3

        logger.info("[snap_status_ws][user(%s)] Fetching final result for request_id=%s", self.user_id, request_id)

        for attempt in range(1, max_retries + 1):
            try:
                response = await client.post(
                    Endpoints.result_endpoint,
                    headers=self.headers,
                    json=request_payload
                )
                response.raise_for_status()

                logger.info(
                    "[snap_status_ws][user(%s)] Final result fetched successfully on attempt %s for request_id=%s",
                    self.user_id, attempt, request_id
                )

                return json.loads(response.text)

            except Exception as e:
                logger.warning(
                    "[snap_status_ws][user(%s)] Attempt %s failed to fetch final result for request_id=%s: %s",
                    self.user_id, attempt, request_id, e
                )

                if attempt == max_retries:
                    logger.error(
                        "[snap_status_ws][user(%s)] All %s attempts failed for request_id=%s",
                        self.user_id, max_retries, request_id
                    )
                    # Return empty dict or raise exception based on your error handling strategy
                    return {}

                await asyncio.sleep(1)

        return {}

    # nao usado atualmente, possibilidade de pensar no uso antes das alteracoes do codigo, "pre-processing"
    async def _save_raw_data(self, data: Dict[str, Any]) -> None:
        """Save raw API response data to MinIO."""
        await save_to_minio(
            bucket_name="pre-reports",
            object_name=f"{self.user_id}_{self.reports_id}.json",
            data=data,
            user_id=self.user_id
        )
        logger.info("[snap_status_ws][user(%s)] Saved raw data to MinIO", self.user_id)

    async def _process_and_save_final_report(self, final_result_json: Dict[str, Any],
                                             report_type: str, report_number: str,
                                             report_search_args: Dict[str, Any]) -> None:
        """Process the final result and save to MinIO."""
        full_data = await self.get_report_metadata(
            final_result_json, report_type, self.reports_id,
            report_search_args, report_number, None  # status_code not used in original
        )

        result_dict = json.loads(full_data.model_dump_json())

        logger.info(
            "[snap_status_ws][user(%s)] Saving final report to MinIO for reports_id=%s",
            self.user_id, self.reports_id
        )

        await save_to_minio(
            bucket_name="reports",
            object_name=f"{self.user_id}_{self.reports_id}.json",
            data=result_dict,
            user_id=self.user_id
        )

    async def _handle_timeout(self, request_id: str) -> None:
        """Handle timeout scenario."""
        # Create a service instance to update the error report
        async with get_database_async_engine().connect() as conn:
            async_session = AsyncSession(conn)
            from services.report_service import UserReportsService
            service = UserReportsService(db=async_session, user_id=self.user_id, user_reports_id=self.reports_id)
            await service.update_error_report(snap_request_id=request_id)

        await try_send_websocket(
            self.connection_manager,
            self.user_id,
            self.reports_id,
            {
                "id": self.reports_id,
                "status_code": SummaryReportStatus.error
            }
        )

    async def _handle_error(self, request_id: str) -> None:
        """Handle general errors."""
        try:
            # Create a service instance to update the error report
            async with get_database_async_engine().connect() as conn:
                async_session = AsyncSession(conn)
                from services.report_service import UserReportsService
                service = UserReportsService(db=async_session, user_id=self.user_id, user_reports_id=self.reports_id)
                await service.update_error_report(snap_request_id=request_id)
            logger.info(
                "[snap_status_ws][user(%s)] Report status updated to error in DB for reports_id=%s",
                self.user_id, self.reports_id
            )
        except Exception as db_err:
            logger.error(
                "[snap_status_ws][user(%s)] Failed to update report status in DB for reports_id=%s: %s",
                self.user_id, self.reports_id, db_err
            )

        try:
            await try_send_websocket(
                self.connection_manager,
                self.user_id,
                self.reports_id,
                {
                    "id": self.reports_id,
                    "status_code": SummaryReportStatus.error
                }
            )
            logger.info(
                "[snap_status_ws][user(%s)] Error message sent via WebSocket for reports_id=%s",
                self.user_id, self.reports_id
            )
        except Exception as ws_err:
            logger.error(
                "[snap_status_ws][user(%s)] Failed to send WebSocket error message for reports_id=%s: %s",
                self.user_id, self.reports_id, ws_err
            )

    def _is_test_case(self, report_type: str, report_search_args: Dict[str, Any]) -> bool:
        """Check if this is a test case."""
        return ReportMockValidator.is_test_case(report_type, report_search_args[report_type])

    def _is_error_case(self, report_type: str, report_search_args: Dict[str, Any]) -> bool:
        """Check if this is an error test case."""
        return ReportMockValidator.is_error_case(report_type, report_search_args[report_type])

    def ensure_report_type_key(self, api_json, key):

        if key not in api_json:
            new_dict = {key: [api_json]}
        else:
            new_dict = api_json

        return new_dict

    async def get_report_metadata(self, api_json, report_type, reports_id, report_search_args, report_number,
                                  status_code) -> SnapApiResponse:

        if report_type.lower() == 'cpf':
            api_json = self.ensure_report_type_key(api_json, 'cpf')
            report_metadata = await self._get_cpf_report_metadata(api_json, report_type, reports_id, report_search_args,
                                                             report_number, status_code)
        elif report_type.lower() == 'cnpj':
            api_json = self.ensure_report_type_key(api_json, 'cnpj')
            report_metadata = await self._get_cnpj_report_metadata(api_json, report_type, reports_id, report_search_args,
                                                              report_number, status_code)
        elif report_type.lower() == 'telefone':
            api_json = self.ensure_report_type_key(api_json, 'telefone')
            report_metadata = await self._get_telmail_report_metadata(api_json, report_type, reports_id, report_search_args,
                                                              report_number, status_code)
        elif report_type.lower() == 'email':
            api_json = self.ensure_report_type_key(api_json, 'email')
            report_metadata = await self._get_telmail_report_metadata(api_json, report_type, reports_id, report_search_args,
                                                              report_number, status_code)
        else:
            raise ReportNotImplementedError(report_type)

        return report_metadata

    async def _get_telmail_report_metadata(self, api_json, report_type, reports_id, report_search_args, report_number,
                                           status_code) -> SnapApiResponse:
        # Robustly check for required fields before accessing pessoa_buscada
        try:
            if report_type not in api_json:
                pessoa_buscada = ''
            elif not isinstance(api_json[report_type], list) or len(api_json[report_type]) == 0:
                pessoa_buscada = ''
            else:
                snap_list = api_json[report_type][0].get('SNAP')
                if not isinstance(snap_list, list) or len(snap_list) == 0:
                    pessoa_buscada = ''
                elif 'pessoa' not in snap_list[0]:
                    pessoa_buscada = ''
                else:
                    pessoa_buscada = snap_list[0]['pessoa']
        except KeyError as e:
            logger.error(f"[_get_telmail_report_metadata] Error accessing pessoa_buscada: {e}")
            pessoa_buscada = ''

        multiple_values = "Múltiplos Valores"

        final_result_json_idade = None
        final_result_json_sexo = ""
        final_result_json_nome = ""
        final_result_json_nome_mae = ""

        logger.info("[_get_telmail_report_metadata] Searching for pessoa unica")

        if len(pessoa_buscada) == 1:
            pessoa = pessoa_buscada[0]
            final_result_json_idade = pessoa.get('idade', None)
            final_result_json_sexo = pessoa.get('sexo', "")
            final_result_json_nome = pessoa.get('full name', "")
            if 'pessoa' in pessoa:
                for mae in pessoa['pessoa']:
                    if mae.get('label default key') == 'parente MAE' and 'full name' in mae:
                        final_result_json_nome_mae = mae['full name']

        elif len(pessoa_buscada) > 1:
            final_result_json_idade = multiple_values
            final_result_json_sexo = multiple_values
            final_result_json_nome = multiple_values
            final_result_json_nome_mae = multiple_values

        now = datetime.now(timezone.utc).isoformat()
        status_code_formated = ReportStatus.normalize(status_code)
        logger.info("[_get_telmail_report_metadata] Building SnapApiResponse object for report=%s", reports_id)
        full_data = SnapApiResponse(
            report_name=f"{report_type} {report_number}",
            report_type=report_type,
            user_reports_id=str(reports_id),
            report_status=status_code_formated,
            report_search_args=report_search_args,
            subject_name=final_result_json_nome,
            subject_mother_name=final_result_json_nome_mae,
            subject_age=final_result_json_idade,
            subject_sex=final_result_json_sexo,
            created_at=now,
            modified_at=now,
            omitted_notes=[],
            data=api_json
        )

        return full_data

    async def _get_cnpj_report_metadata(self, api_json, report_type, reports_id, report_search_args, report_number,
                                        status_code) -> SnapApiResponse:

        # Robustly check for required fields before accessing empresa_buscada
        try:
            if report_type not in api_json:
                empresa_buscada = ''
            elif not isinstance(api_json[report_type], list) or len(api_json[report_type]) == 0:
                empresa_buscada = ''
            else:
                snap_list = api_json[report_type][0].get('SNAP')
                if not isinstance(snap_list, list) or len(snap_list) == 0:
                    empresa_buscada = ''
                elif 'empresa' not in snap_list[0]:
                    empresa_buscada = ''
                else:
                    empresa_buscada = snap_list[0]['empresa']
        except KeyError as e:
            logger.error(f"[_get_cnpj_report_metadata] Error accessing empresa_buscada: {e}")
            empresa_buscada = ''

        # Extracting empresa data
        final_result_json_idade = None
        final_result_json_sexo = ""
        final_result_json_nome = ""
        final_result_json_nome_mae = ""

        logger.info("[_get_cnpj_report_metadata] Searching for empresa with bookmark 4")
        if len(empresa_buscada) > 0:
            logger.info(f"[_get_cnpj_report_metadata] Found {len(empresa_buscada)} empresas to process.")
            for empresa in empresa_buscada:
                if 'bookmark' in empresa and empresa['bookmark'] == 4:
                    logger.info(f"[_get_cnpj_report_metadata] Found empresa with bookmark 4: {empresa}")
                    final_result_json_nome = empresa.get('razao social', "")
        else:
            logger.warning("[_get_cnpj_report_metadata] No empresas found in empresa_buscada.")

        now = datetime.now(timezone.utc).isoformat()
        status_code_formated = ReportStatus.normalize(status_code)
        logger.info("[_get_cnpj_report_metadata] Building SnapApiResponse object for report=%s", reports_id)
        full_data = SnapApiResponse(
            report_name=f"{report_type} {report_number}",
            report_type=report_type,
            user_reports_id=str(reports_id),
            report_status=status_code_formated,
            report_search_args=report_search_args,
            subject_name=final_result_json_nome,
            subject_mother_name=final_result_json_nome_mae,
            subject_age=final_result_json_idade,
            subject_sex=final_result_json_sexo,
            created_at=now,
            modified_at=now,
            omitted_notes=[],
            data=api_json
        )

        return full_data

    async def _get_cpf_report_metadata(self, api_json, report_type, reports_id, report_search_args, report_number,
                                       status_code) -> SnapApiResponse:

        # Robustly check for required fields before accessing pessoa_buscada
        try:
            if report_type not in api_json:
                pessoa_buscada = ''
            elif not isinstance(api_json[report_type], list) or len(api_json[report_type]) == 0:
                pessoa_buscada = ''
            else:
                snap_list = api_json[report_type][0].get('SNAP')
                if not isinstance(snap_list, list) or len(snap_list) == 0:
                    pessoa_buscada = ''
                elif 'pessoa' not in snap_list[0]:
                    pessoa_buscada = ''
                else:
                    pessoa_buscada = snap_list[0]['pessoa']
        except KeyError as e:
            logger.error(f"[_get_cpf_report_metadata] Error accessing pessoa_buscada: {e}")
            pessoa_buscada = ''

        # Extracting person data
        final_result_json_idade = None
        final_result_json_sexo = ""
        final_result_json_nome = ""
        final_result_json_nome_mae = ""

        logger.info("[_get_cpf_report_metadata] Searching for pessoa with bookmark 4")
        if len(pessoa_buscada) > 0:
            logger.info(f"[_get_cpf_report_metadata] Found {len(pessoa_buscada)} pessoas to process.")
            # Only extract from the first pessoa if bookmark 4 is not found below
            pessoa = pessoa_buscada[0]
            final_result_json_idade = pessoa.get('idade', None)
            final_result_json_sexo = pessoa.get('sexo', "")
            final_result_json_nome = pessoa.get('full name', "")
            if 'pessoa' in pessoa:
                for mae in pessoa['pessoa']:
                    if mae.get('label default key') == 'parente MAE' and 'full name' in mae:
                        final_result_json_nome_mae = mae['full name']
        else:
            logger.warning("[_get_cpf_report_metadata] No pessoas found in pessoa_buscada.")

        # Overwrite with bookmark 4 if found
        for pessoa in pessoa_buscada:
            if 'bookmark' in pessoa and pessoa['bookmark'] == 4:
                logger.info(f"[_get_cpf_report_metadata] Found pessoa with bookmark 4: {pessoa}")
                final_result_json_idade = pessoa.get('idade', None)
                final_result_json_sexo = pessoa.get('sexo', "")
                final_result_json_nome = pessoa.get('full name', "")
                if 'pessoa' in pessoa:
                    for mae in pessoa['pessoa']:
                        if mae.get('label default key') == 'parente MAE' and 'full name' in mae:
                            final_result_json_nome_mae = mae['full name']

        now = datetime.now(timezone.utc).isoformat()
        status_code_formated = ReportStatus.normalize(status_code)
        logger.info("[_get_cpf_report_metadata] Building SnapApiResponse object for report=%s", reports_id)
        full_data = SnapApiResponse(
            report_name=f"{report_type} {report_number}",
            report_type=report_type,
            user_reports_id=str(reports_id),
            report_status=status_code_formated,
            report_search_args=report_search_args,
            subject_name=final_result_json_nome,
            subject_mother_name=final_result_json_nome_mae,
            subject_age=final_result_json_idade,
            subject_sex=final_result_json_sexo,
            created_at=now,
            modified_at=now,
            omitted_notes=[],
            data=api_json
        )

        return full_data