import logging
from keycloak import KeycloakAdmin
from core.config import settings
from exceptions.business_exceptions import KeycloakAdminInitializationError, KeycloakAdminUnavailableError

logger = logging.getLogger(__name__)

keycloak_admin_client: KeycloakAdmin = None

async def initialize_keycloak_admin_client():
    """Initializes the KeycloakAdmin client and authenticates using client credentials."""
    global keycloak_admin_client
    if keycloak_admin_client is None:
        logger.info("[KeycloakClient] Initializing Keycloak Admin client...")
        try:
            admin_instance = KeycloakAdmin(
                server_url=settings.KEYCLOAK_URL,
                username=None,  # Not needed for client credentials
                password=None,  # Not needed for client credentials
                realm_name=settings.REALM_NAME,
                client_id=settings.CLIENT_ID_KEYCLOAK,
                client_secret_key=settings.CLIENT_SECRET_KEYCLOAK,
                verify=settings.KEYCLOAK_VERIFY_SSL
            )

            keycloak_admin_client = admin_instance
            logger.info("[KeycloakClient] Keycloak Admin client successfully initialized and authenticated using client credentials.")
        except Exception as e:
            logger.exception(f"[KeycloakClient] Failed to initialize Keycloak Admin client: {e}")
            raise KeycloakAdminInitializationError(str(e))


async def shutdown_keycloak_admin_client():
    """Logs out the Keycloak Admin client."""
    global keycloak_admin_client
    if keycloak_admin_client:
        logger.info("[KeycloakClient] Shutting down Keycloak Admin client...")
        try:
            # For client credentials, explicit logout might not be as critical
            # as the token will expire. However, if the library supports it,
            # it's good practice. `logout_admin_client` is often for password grants.
            # For client_credentials, the token is obtained via `get_token_by_client_credentials`
            # and automatically refreshed. There's no session to "log out" in the same way.
            # You can simply set the client to None.
            pass # No explicit logout needed for client_credentials if the token expires

            logger.info("[KeycloakClient] Keycloak Admin client instance cleared.")
        except Exception as e:
            logger.warning(f"[KeycloakClient] Error during Keycloak Admin client shutdown: {e}")
        finally:
            keycloak_admin_client = None

def get_keycloak_admin() -> KeycloakAdmin:
    """FastAPI dependency to get the initialized KeycloakAdmin client."""
    if keycloak_admin_client is None:
        logger.error("[KeycloakClient] Keycloak Admin client requested before initialization or after shutdown.")
        raise KeycloakAdminUnavailableError()
    return keycloak_admin_client