import { REPORTS_CLIENT } from "../clients/reports.client";
import { AxiosError } from "axios";

const redirectUrl = import.meta.env.VITE_REPORTS_API_URL;

export interface LoginCredentials {
  provider?: "microsoft" | "google";
  email?: string;
  password?: string;
}

export interface LogoutResponse {
  message: string;
}

export const redirectToAuthServer = async (credentials: LoginCredentials) => {
  if (credentials.provider) {
    window.location.href = `${redirectUrl}/auth/${credentials.provider}?redirect_url=${window.location.origin}`;
  }
};

export const postLogout = async (): Promise<LogoutResponse> => {
  try {
    const response = await REPORTS_CLIENT.post<LogoutResponse>("/logout", {});
    return response.data;
  } catch (error) {
    console.error("[postLogout] Logout failed", error);

    // Evitar loop infinito de logout
    if ((error as AxiosError)?.response?.status === 401) {
      console.log("[postLogout] User already logged out (401), treating as success");
      return { message: "User already logged out" };
    }
    throw error;
  }
};
