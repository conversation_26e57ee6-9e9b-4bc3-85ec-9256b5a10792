import json
import logging

from pyspark.sql import SparkSession
from pyspark.sql.functions import col, from_json
from pyspark.sql.types import StructType, StructField, StringType, ArrayType

import reports_processor.constants
from reports_processor.constants import MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, SPARK_CHECKPOINT_REPORTS, spark
from reports_processor.processor import handle_batch, debug_processing

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s %(levelname)s: %(message)s')

logging.getLogger("py4j").setLevel(logging.WARNING)
logging.getLogger("py4j.java_gateway").setLevel(logging.WARNING)


#TODO: mandar so cpf sem mascara


def main():
    """Main function to start the Spark processing job."""
    reports_processor.constants.spark = SparkSession.builder.appName("ReportProcessor").getOrCreate()

    # Configure MinIO access
    hadoop_conf = reports_processor.constants.spark._jsc.hadoopConfiguration()
    hadoop_conf.set("fs.s3a.endpoint", MINIO_ENDPOINT)
    hadoop_conf.set("fs.s3a.access.key", MINIO_ACCESS_KEY)
    hadoop_conf.set("fs.s3a.secret.key", MINIO_SECRET_KEY)
    hadoop_conf.set("fs.s3a.path.style.access", "true")
    hadoop_conf.set("fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")

    # Read from Kafka
    kafka_df = reports_processor.constants.spark.readStream \
        .format("kafka") \
        .option("kafka.bootstrap.servers", "kafka:9092") \
        .option("subscribe", "reports") \
        .option("startingOffsets", "earliest") \
        .option("failOnDataLoss", "false") \
        .load()

    # Define schema for S3 event records
    schema = StructType([
        StructField("EventName", StringType()),
        StructField("Key", StringType()),
        StructField("Records", ArrayType(
            StructType([
                StructField("s3", StructType([
                    StructField("bucket", StructType([
                        StructField("name", StringType())
                    ])),
                    StructField("object", StructType([
                        StructField("key", StringType())
                    ]))
                ]))
            ])
        ))
    ])

    # Parse records
    raw_df = kafka_df.selectExpr("CAST(value AS STRING) as raw_json")
    parsed_df = raw_df.select(from_json(col("raw_json"), schema).alias("data"))

    # Extract bucket and key information
    paths_df = parsed_df.select(
        col("data.Records")[0]["s3"]["bucket"]["name"].alias("bucket"),
        col("data.Records")[0]["s3"]["object"]["key"].alias("key")
    )

    # Process each batch
    query = paths_df.writeStream \
        .foreachBatch(handle_batch) \
        .option("checkpointLocation", SPARK_CHECKPOINT_REPORTS) \
        .outputMode("append") \
        .start()

    query.awaitTermination()


if __name__ == '__main__':
    main()
    # debug_processing()