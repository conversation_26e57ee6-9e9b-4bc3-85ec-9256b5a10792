import { create } from "zustand";
import { REPORT_SECTIONS } from "~/helpers/constants";
import { ReportMetadata, ReportSection } from "~/types/global";
import { produce } from "immer";
import { getGlobalForceSave } from "~/hooks/useReportActionsWithAutoSave";

export type SectionListRecordsItem = {
  title: string;
  data_count: number;
};

type UpdateFnPromise = (entry: any, index?: number) => void;

interface ReportDetailStoreActions {
  setReportSections: (sections: ReportSection[]) => void;
  setDeletedSections: (sections: ReportSection[]) => void;
  setReportType: (type: string) => void;
  setMetadata: (metadata: ReportMetadata) => void;
  setProfileImage: (image: string) => void;
  resetReportDetailStore: () => void;
  forceReload: () => void;
  updateSectionEntries: (
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number,
    includeSubsections?: boolean,
    crossSectionUpdate?: { matchingProp: string; updaterFn: UpdateFnPromise }
  ) => void;
  updateSubsectionWithMainSection: (
    sectionTitle: string,
    subsectionName: string,
    matchingProp: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number
  ) => void;
  scheduleAutoSave: () => void;
  cancelAutoSave: () => void;
  forceSave: () => void;
  isPendingSave: () => boolean;
  hasPendingChanges: () => boolean;
}

interface ReportDetailStoreState {
  sections: ReportSection[];
  deletedSections: ReportSection[];
  metadata: ReportMetadata | null;
  sectionListRecords: SectionListRecordsItem[];
  totalRecords: number;
  reportType: string;
  profileImage: string | null;
  reloadTrigger: number;
  actions: ReportDetailStoreActions;
  _autoSave: {
    timeoutId: NodeJS.Timeout | null;
    isPending: boolean;
    hasPendingChanges: boolean;
    mutation: any;
  };
}

const initialState = {
  sections: [] as ReportSection[],
  deletedSections: [] as ReportSection[],
  metadata: null as ReportMetadata | null,
  sectionListRecords: [] as SectionListRecordsItem[],
  totalRecords: 0,
  reportType: "",
  profileImage: null as string | null,
  reloadTrigger: 0,
  _autoSave: {
    timeoutId: null as NodeJS.Timeout | null,
    isPending: false,
    hasPendingChanges: false,
    mutation: null as any,
  },
};

const useReportDetailStore = create<ReportDetailStoreState>((set, get) => ({
  ...initialState,
  actions: {
    resetReportDetailStore: () => set(() => initialState),
    forceReload: () => set((state) => ({ ...state, reloadTrigger: state.reloadTrigger + 1 })),
    setReportSections: (sections) => {
      const imagensSection = sections.find(
        (section) => section.title === REPORT_SECTIONS.imagens
      );

      let profileImage = null;
      if (imagensSection) {
        const firstImage = imagensSection.data[0]?.detalhes?.find(
          (d: any) => d.value?.url
        );
        if (firstImage) {
          profileImage = firstImage.value.url.value;
        }
      }

      set({ profileImage });

      const sectionListRecords = sections
        .filter(section => {
          if (section.subsection) return false;
          if (section.is_deleted) return false;
          if (section.data_count > 0) return true;

          // Algumas seções vazias vem com a chave "detalhes" vazia dentro de "data", por isso a verificação abaixo
          return Array.isArray(section.data) &&
            section.data.some((item) =>
              Array.isArray((item as any).detalhes) &&
              (item as any).detalhes.length > 0
            );
        })
        .map((s) => ({ title: s.title, data_count: s.data_count }));

      const totalRecords = sectionListRecords.reduce((sum, section) => {
        const count = Number(section.data_count) || 0;
        return sum + count;
      }, 0);

      set({ sections });
      set({ sectionListRecords });
      set({ totalRecords });
    },

    setDeletedSections: (deletedSections) => set({ deletedSections }),

    setMetadata: (metadata) => set({ metadata }),

    setReportType: (reportType) => set({ reportType }),

    setProfileImage: (profileImage) => set({ profileImage }),

    updateSectionEntries: (
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn,
      includeSubsections = false,
      crossSectionUpdate
    ) =>
      set((state) =>
        produce(state, (draft) => {
          const sectionsToUpdate = includeSubsections
            ? draft.sections.filter((s) => s.title === sectionTitle)
            : draft.sections.filter((s) => s.title === sectionTitle && !s.subsection);

          const matchingValues: any[] = [];

          sectionsToUpdate.forEach((section) => {
            // 1) apply the mutation you passed, *with* its index
            section.data.forEach((entry, i) => {
              updaterFn(entry as any, i);
              if (crossSectionUpdate && (entry as any)[crossSectionUpdate.matchingProp]?.value) {
                matchingValues.push((entry as any)[crossSectionUpdate.matchingProp].value);
              }
            });

            // 2) set the section flag however *you* want
            section.is_deleted = testSectionDeletedFn(section);

            if (calculateDataCountFn) {
              section.data_count = calculateDataCountFn(section);
            }
          });

          // Handle cross-section updates
          if (crossSectionUpdate && matchingValues.length > 0) {
            const mainSection = draft.sections.find((s) => s.title === sectionTitle && !s.subsection);
            if (mainSection) {
              mainSection.data.forEach((entry, i) => {
                if (matchingValues.includes((entry as any)[crossSectionUpdate.matchingProp]?.value)) {
                  crossSectionUpdate.updaterFn(entry as any, i);
                }
              });
              mainSection.is_deleted = testSectionDeletedFn(mainSection);
              if (calculateDataCountFn) {
                mainSection.data_count = calculateDataCountFn(mainSection);
              }
            }
          }
        })
      ),

    updateSubsectionWithMainSection: (
      sectionTitle,
      subsectionName,
      matchingProp,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    ) =>
      set((state) =>
        produce(state, (draft) => {
          const subsection = draft.sections.find(
            (s) => s.title === sectionTitle && s.subsection === subsectionName
          );
          const mainSection = draft.sections.find(
            (s) => s.title === sectionTitle && !s.subsection
          );

          if (!subsection || !mainSection) return;

          // Get matching values from subsection
          const matchingValues = subsection.data.map((entry: any) => entry[matchingProp]?.value).filter(Boolean);

          // Update subsection
          subsection.data.forEach((entry, i) => updaterFn(entry as any, i));
          subsection.is_deleted = testSectionDeletedFn(subsection);
          if (calculateDataCountFn) {
            subsection.data_count = calculateDataCountFn(subsection);
          }

          // Update matching entries in main section
          mainSection.data.forEach((entry, i) => {
            if (matchingValues.includes((entry as any)[matchingProp]?.value)) {
              updaterFn(entry as any, i);
            }
          });
          mainSection.is_deleted = testSectionDeletedFn(mainSection);
          if (calculateDataCountFn) {
            mainSection.data_count = calculateDataCountFn(mainSection);
          }
        })
      ),

    scheduleAutoSave: () => {
      const state = get();
      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          hasPendingChanges: true,
        }
      }));

      if (state._autoSave.isPending && state._autoSave.mutation) {
        state._autoSave.mutation.reset();
        set((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
          }
        }));
      }

      if (state._autoSave.timeoutId) {
        clearTimeout(state._autoSave.timeoutId);
      }

      const timeoutId = setTimeout(() => {
        const forceSave = getGlobalForceSave();
        if (forceSave) {
          forceSave();
        }
      }, 5000);

      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          timeoutId,
        }
      }));
    },

    cancelAutoSave: () => {
      const state = get();
      if (state._autoSave.timeoutId) {
        clearTimeout(state._autoSave.timeoutId);
      }
      set((state) => ({
        ...state,
        _autoSave: {
          ...state._autoSave,
          timeoutId: null,
          hasPendingChanges: false,
        }
      }));
    },

    forceSave: () => {
      // This will be set by useReportActionsWithAutoSave when it's initialized
      console.warn("forceSave not yet initialized");
    },

    isPendingSave: () => get()._autoSave.isPending,

    hasPendingChanges: () => get()._autoSave.hasPendingChanges,
  },
}));

export { useReportDetailStore };

export const useReportSections = () =>
  useReportDetailStore((state) => state.sections);
export const useReportDeletedSections = () =>
  useReportDetailStore((state) => state.deletedSections);
export const useReportMetadata = () =>
  useReportDetailStore((state) => state.metadata);
export const useReportType = () =>
  useReportDetailStore((state) => state.reportType);
export const useReportProfileImage = () =>
  useReportDetailStore((state) => state.profileImage);
export const useReportDetailActions = () =>
  useReportDetailStore((state) => state.actions);
export const useReportSectionListRecords = () =>
  useReportDetailStore((state) => state.sectionListRecords);
export const useTotalRecords = () =>
  useReportDetailStore((state) => state.totalRecords);
export const useReportReloadTrigger = () =>
  useReportDetailStore((state) => state.reloadTrigger);