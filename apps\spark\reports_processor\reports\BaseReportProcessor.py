from abc import ABC, abstractmethod
from typing import List, Dict, Any
from dataclasses import dataclass
from copy import copy

from ..CallbackProcessors import CallbackProcessors
from ..EntityExtractor import EntityExtractor, VinculoConfig, ExtractionResult
from ..constants import ReportKeys, rede_social_key_prefix, age_fields
from ..FormatFrontFormat import VinculoSection, front_vinculo_pessoais, front_p_pessoas_relacionadas, front_empresas_relacionadas
from datetime import datetime
import logging

@dataclass
class ReportTypeConfig:
    """Configuration for a specific report type"""
    do_filter_doc: str
    do_filter_name: str
    title_format: str

    enabled_sections: List[VinculoSection]

    def __post_init__(self):
        # Set the front_do_doc title format
        if self.do_filter_doc:
            front_do_doc = VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_DOC)
            front_do_doc.title = front_do_doc.title.format(self.title_format)


class BaseReportProcessor(ABC):
    """Base class for report processors"""

    def __init__(self, extractor: EntityExtractor):
        self.extractor = extractor

    @abstractmethod
    def get_config(self) -> ReportTypeConfig:
        """Get the configuration for this report type"""
        pass

    @abstractmethod
    def get_custom_vinculos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[
        str, VinculoConfig]:
        pass

    def _get_custom_processos(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[Any, VinculoConfig]:
        # Extract complex relationships first
        basic_processos = self.extractor.extract_vinculos_genericos(
            other_result_data, entity_type, search_value,
            VinculoConfig(extract_type=ReportKeys.PROCESSO,
                          skip_lists=False,
                          item_callback=CallbackProcessors.processos_item_callback,
                          extra_data_callback=CallbackProcessors.processos_extra_vinculos_callback)
        )

        if not basic_processos.data:
            return {}

        custom_processos = {
            VinculoSection.PROCESSOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.PROCESSOS),
                extract_func=lambda *args, **kwargs: ExtractionResult(basic_processos.data['base'], basic_processos.sources),
                extract_type=ReportKeys.PROCESSO
            )
        }

        # Add process-specific vinculos
        if hasattr(basic_processos.data, 'get') and 'extra' in basic_processos.data:
            basic_processos_vinculos = basic_processos.data.get('extra', {})
            for p_key in basic_processos_vinculos.keys():
                new_p = copy(VinculoSection.front_format_section(VinculoSection.PROCESSOS))
                new_p.subsection = p_key

                custom_processos[f'processos_{p_key}'] = VinculoConfig(
                    new_p,
                    extract_func=lambda *args, key=p_key, **kwargs: ExtractionResult(basic_processos_vinculos[key], basic_processos.sources),
                    extract_type=ReportKeys.PROCESSO
                )

        return custom_processos


    def _get_custom_fornecimentos_campanha(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[VinculoSection, VinculoConfig]:

        fornecimentos = self.extractor.extract_vinculos_campanha(other_result_data, entity_type, None,
                                                            VinculoConfig(reverse=True))

        return {
            VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.FORNECIMENTOS_ELEITORAIS_ENVIADAS),
                extract_func=lambda *args, **kwargs: ExtractionResult(fornecimentos.data['enviada'], fornecimentos.sources),
                extract_type="FORNECIMENTOS"
            ),
            VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.FORNECIMENTOS_ELEITORAIS_RECEBIDAS),
                extract_func=lambda *args, **kwargs: ExtractionResult(fornecimentos.data['recebida'], fornecimentos.sources),
                extract_type="FORNECIMENTOS"
            )
        }

    def _get_custom_doacoes_campanha(self, other_result_data: Dict, entity_type: str, search_value: str) -> Dict[VinculoSection, VinculoConfig]:
        # Extract complex relationships first

        doacoes = self.extractor.extract_vinculos_campanha(other_result_data, entity_type, 'Doacoes',
                                                           VinculoConfig())

        return {
        VinculoSection.DOACOES_ELEITORAIS_ENVIADAS: VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DOACOES_ELEITORAIS_ENVIADAS),
            extract_func=lambda *args, **kwargs: ExtractionResult(doacoes.data['enviada'], doacoes.sources),
            extract_type="DOACOES"
        ),
        VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS: VinculoConfig(
            VinculoSection.front_format_section(VinculoSection.DOACOES_ELEITORAIS_RECEBIDAS),
            extract_func=lambda *args, **kwargs: ExtractionResult(doacoes.data['recebida'], doacoes.sources),
            extract_type="DOACOES",
        )}

    def get_section_vinculo_config(self, other_result_data: Dict, entity_type: str, search_value: str, has_main_data: bool = True) -> Dict[
        Any, VinculoConfig]:
        config = self.get_config()
        vinculos = self._get_basic_section_vinculo_config(config, has_main_data)
        custom_vinculos = self.get_custom_vinculos(other_result_data, entity_type, search_value)
        vinculos.update(custom_vinculos)

        result = {x: vinculos[x] for x in config.enabled_sections if x in vinculos}
        for x, v in vinculos.items():
            if x not in result and not isinstance(x, VinculoSection):
                result[x] = v

        return result

    def _get_basic_section_vinculo_config(self, config=None, has_main_data: bool = True):
        if config is None:
            config = self.get_config()

        if has_main_data:
            vinculos =  {

            VinculoSection.REDES_SOCIAIS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.REDES_SOCIAIS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=None,
                extract_type_starts_with=rede_social_key_prefix,
                filter_base_data_callback=CallbackProcessors.filter_for_redes_sociais_vinculos,
                skip_lists=False,
            ),
            VinculoSection.VINCULOS_EDUCACIONAIS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.VINCULOS_EDUCACIONAIS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.EMPRESA,
                filter_base_data_callback=CallbackProcessors.filter_for_vinculos_educacionais,
                item_callback=CallbackProcessors.vinculos_educacionais_item_callback
            ),

            VinculoSection.OUTRAS_URLS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.OUTRAS_URLS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.URL,
            ),

            VinculoSection.NOMES_USUARIO: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.NOMES_USUARIO),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.ALIAS,
            ),

            VinculoSection.IMAGENS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.IMAGENS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.IMAGEM,
            ),

            VinculoSection.JUNTAS_COMERCIAIS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.JUNTAS_COMERCIAIS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.EMPRESA_JUCESP,
            ),
            VinculoSection.PHONES: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.PHONES),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.TELEFONE,
            ),
            VinculoSection.EMAILS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.EMAILS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.EMAIL_ENTITY,
            ),
            VinculoSection.ENDERECOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.ENDERECOS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.ENDERECO,
            ),
            VinculoSection.MANDADOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.MANDADOS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.MANDADO,
                skip_lists=False,
                #item_callback=CallbackProcessors.mandados_item_callback
            ),
            VinculoSection.PARENTES: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.PARENTES),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.PESSOA,
                skip_lists=False,
                try_merge=True,
                filter_base_data_callback=CallbackProcessors.filter_for_parentes_vinculos
            ),
            VinculoSection.VINCULOS_EMPREGATICIOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.VINCULOS_EMPREGATICIOS),
                extract_func=self.extractor.extract_vinculos_empregaticios,
                extract_type=ReportKeys.REMUNERACAO,
            ),
            VinculoSection.RECURSOS_RECEBIDOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.RECURSOS_RECEBIDOS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.RECURSOS_RECEBIDOS,
                skip_lists=True,
                item_callback=CallbackProcessors.recursos_recebidos_item_callback
            ),
            VinculoSection.SERVICO_PUBLICO: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.SERVICO_PUBLICO),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.SERVIDOR,
                skip_lists=False,
            ),
            VinculoSection.DIARIOS_OFICIAIS_DOC: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_DOC),

                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.DIARIO_OFICIAL,
                filter_source_name=config.do_filter_doc,
                try_merge=False
            ),
            VinculoSection.DIARIOS_OFICIAIS_NOME: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.DIARIOS_OFICIAIS_NOME),

                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.DIARIO_OFICIAL,
                filter_source_name=config.do_filter_name,
                try_merge=False
            ),
            VinculoSection.FILIACAO_PARTIDARIA: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.FILIACAO_PARTIDARIA),

                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.PARTIDO_POLITICO,
                try_merge=True,
                skip_lists=False
            ),
            VinculoSection.OUTROS_CONTATOS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.OUTROS_CONTATOS),

                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.PESSOA,
                try_merge=True,
                skip_lists=False,
                filter_base_data_callback=CallbackProcessors.filter_for_outros_contatos_vinculo
            ),
            VinculoSection.PAIS: VinculoConfig(
                VinculoSection.front_format_section(VinculoSection.PAIS),
                extract_func=self.extractor.extract_vinculos_genericos,
                extract_type=ReportKeys.PAI,
                try_merge=False
            ),
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS: VinculoConfig(
                    VinculoSection.front_format_section(VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS),
                    extract_func=self.extractor.extract_vinculos_genericos,
                    extract_type=ReportKeys.PESSOA,
                    skip_lists=False,
                    filter_base_data_callback=CallbackProcessors.filter_for_not_parentes_vinculos
                ),

                VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS: VinculoConfig(
                    VinculoSection.front_format_section(VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS),
                    extract_func=self.extractor.extract_vinculos_genericos,
                    extract_type=ReportKeys.EMPRESA,
                    skip_lists=False
                )
        }
        else:
            vinculos = {
                VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS: VinculoConfig(
                    VinculoSection.front_format_section(VinculoSection.POSSIVEIS_PESSOAS_RELACIONADAS),
                    extract_func=self.extractor.extract_vinculos_genericos,
                    extract_type=ReportKeys.PESSOA,
                    skip_lists=False,
                    filter_base_data_callback=CallbackProcessors.filter_for_not_parentes_vinculos
                ),

                VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS: VinculoConfig(
                    VinculoSection.front_format_section(VinculoSection.POSSIVEIS_EMPRESAS_RELACIONADAS),
                    extract_func=self.extractor.extract_vinculos_genericos,
                    extract_type=ReportKeys.EMPRESA,
                    skip_lists=False
                )
            }
        return vinculos

    def adjust_metadata(self, processed_result, metadata):
        return


    @classmethod
    def find_section_in_final_result(cls, section, processed_result):
        r_section = [data for data in processed_result if data['title'] == section.title]
        return r_section[0] if r_section else None

    def _adjust_metadata_age(self, processed_result, metadata):
        personal_data = self.find_section_in_final_result(front_vinculo_pessoais, processed_result)
        if personal_data['data_count'] <= 0:
            return

        for p_age in age_fields:
            if p_age in personal_data['data'][0]['detalhes']:
                tmp_data = personal_data['data'][0]['detalhes'][p_age]['value']
                try:
                    tmp_data = datetime.strptime(tmp_data, "%d/%m/%Y").date()
                    metadata['subject_age'] = tmp_data.isoformat()
                    break
                except Exception:
                    logging.debug(f"Failed to process date {personal_data['data'][0]['detalhes'][p_age]['value']}")
                    continue
        return

    def _adjust_metadata_for_multiple_results(self, processed_result, metadata):
        personal_data = self.find_section_in_final_result(front_vinculo_pessoais, processed_result)
        if personal_data['data_count'] > 0:
            return

        possible_person = self.find_section_in_final_result(front_p_pessoas_relacionadas, processed_result)
        possible_person_count = possible_person['data_count']

        possible_company = self.find_section_in_final_result(front_empresas_relacionadas, processed_result)
        possible_company_count = possible_company['data_count']

        if possible_person_count > 0 or possible_company_count > 0:
            name = "Multiplos registros encontrados"

        else:
            name = "Nenhum registro encontrado"

        metadata['subject_name'] = name
        metadata['subject_mother_name'] = None
        metadata['subject_age'] = None
        metadata['subject_sex'] = None
        metadata['subject_person_count'] = possible_person_count
        metadata['subject_company_count'] = possible_company_count
        # todo: imagem
        return