import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from models.organization_model import Organizations

from services.base_service import BaseService
from exceptions.business_exceptions import OrganizationDatabaseOperationError

logger = logging.getLogger(__name__)

class OrganizationService(BaseService):

    def __init__(self, db: AsyncSession, organization_id: str) -> None:
        super().__init__(db)
        self.organization_id=organization_id
        logger.debug("[OrganizationService] Initialized with organization_id: %s", organization_id)

    async def get_organization_data(self):
        """
        Get the data from organization table.
            
        Returns:
            str: The organization data
            
        Raises:
            HTTPException: If organization is not found
        """
        logger.info("[OrganizationService] Retrieving organization info for organization: %s", self.organization_id)
        try:
            logger.debug("[OrganizationService] Building select query for organization_id: %s", self.organization_id)
            query = (
                select(Organizations)
                .where(
                    Organizations.organization_id == self.organization_id,
                )
            )
            logger.debug("[OrganizationService] Query built: %s", str(query))
            logger.debug("[OrganizationService] Executing organization query for organization: %s", self.organization_id)
            result = await self.db.execute(query)
            logger.debug("[OrganizationService] Query executed, processing results for organization: %s", self.organization_id)
            org = result.scalars().first()  
            if org:
                logger.info("[OrganizationService] Found organization: %s for organization_id: %s", org, self.organization_id)
            else:
                logger.warning("[OrganizationService] No organization found for organization_id: %s", self.organization_id)
            return org  
        except Exception as e:
            logger.error(
                "[OrganizationService] Failed to get organization_id for organization %s. Error: %s",
                self.organization_id,
                str(e),
                exc_info=True
            )
            raise OrganizationDatabaseOperationError(str(e))

