import { PiFilePdf } from "react-icons/pi";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FabButton } from "./base/fab-button";
import { useVisibleReportSections, useReportMetadata, useProfileImage } from "../../context/ReportContext";
import { useReportPDF } from "../../hooks/useReportPDF";
import { toast } from "sonner";
import { REPORT_CONSTANTS } from "../../config/constants";

const PrintButton: React.FC = () => {
  const sections = useVisibleReportSections();
  const metadata = useReportMetadata();
  const profileImage = useProfileImage();
  const { generatePDF, loading } = useReportPDF();

  const handleDownload = async () => {
    let toastId = null;
    try {
      toastId = toast.loading("Gerando PDF...", {
        description: "Isso pode levar alguns minutos. Por favor, não feche esta aba até o download terminar.",
      });

      // usar o web worker para gerar o PDF
      const blobUrl = await generatePDF({ sections, metadata, profile_image: profileImage });

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `${metadata[REPORT_CONSTANTS.new_report.report_name]}.pdf`;
      link.click();
      URL.revokeObjectURL(blobUrl);

      toast.dismiss(toastId);
      toast.success("PDF gerado com sucesso!");
    } catch (err) {
      console.error("Error in download process:", err);
      if (toastId) toast.dismiss(toastId);
      toast.error("Erro ao gerar o PDF. Favor tentar novamente.");
    }
  };

  return (
    <FabButton
      onClick={handleDownload}
      className={`${loading ? "cursor-not-allowed" : "cursor-pointer"}`}
      icon={loading ?
        <AiOutlineLoading3Quarters size={20} className="animate-spin" /> :
        <PiFilePdf size={20} />
      }
      ariaLabel={loading ? "Generating PDF..." : "Print PDF"}
      title="Download PDF"
      position="middleRight"
      disabled={loading}
    />
  );
};

export default PrintButton;