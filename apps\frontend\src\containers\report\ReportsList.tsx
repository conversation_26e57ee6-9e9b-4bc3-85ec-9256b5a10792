import ReportCard from "~/containers/report/ReportCard";
import { ReportData } from "~/types/global";
import { AnimatedFilledButton, Button, Icon } from "@snap/design-system";
import { Plus } from "lucide-react";
import MasonryLayout from "react-layout-masonry";
import { useEffect, useRef, useState } from "react";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useReportListActions, useReportListLoadMore } from "~/store/reportListStore";
import EmptyList from "~/components/EmptyList";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

interface ReportListProps {
  list: ReportData[];
  isFetched: boolean;
  onNewReport: () => void;
}

export default function ReportsList({
  list,
  onNewReport,
  isFetched,
}: ReportListProps) {
  const { checkPermission } = usePermissionCheck();
  const canCreateReport = checkPermission(Permission.CREATE_REPORT);
  const { invalidateReports } = useReportCRUD();
  const { incrementPage } = useReportListActions();
  const loadMore = useReportListLoadMore();
  const [showLoadMore, setShowLoadMore] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  const columnsBreakpoints = {
    346: 1, // 282 + 64 (padding)
    628: 2, // (282 * 2) + 20 + 64
    930: 3, // (282 * 3) + (20 * 2) + 64
    1232: 4, // (282 * 4) + (20 * 3) + 64
    1534: 5, // (282 * 5) + (20 * 4) + 64
    1836: 6, // (282 * 6) + (20 * 5) + 64
    2138: 7, // (282 * 7) + (20 * 6) + 64
    2440: 8, // (282 * 8) + (20 * 7) + 64
    2742: 9, // (282 * 9) + (20 * 8) + 64
  };

  useEffect(() => {
    const section = sectionRef.current;
    if (section) {
      section.addEventListener("scroll", handleScroll);
      return () => section.removeEventListener("scroll", handleScroll);
    }
  }, [loadMore]);

  const loadMoreData = async () => {
    incrementPage()
  }

  const handleLoadMore = async () => {
    console.log("load more");
    await loadMoreData();
    invalidateReports();
  };

  const handleScroll = () => {
    if (!sectionRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = sectionRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 100;
    const isAtTop = scrollTop === 0;
    setShowLoadMore(isAtBottom && !isAtTop);
  };

  return (
    <section
      ref={sectionRef}
      className="flex-1 w-full overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-232px)] px-8 pt-2 pb-8"
    >
      <MasonryLayout columns={columnsBreakpoints} gap={20} className="relative">
        {canCreateReport ? (
          <AnimatedFilledButton
            data-testid="button-new-report"
            onClick={onNewReport}
            icon={<Icon src="/icons/icone_bigplus.svg" />}
          >
            <div className="flex flex-col items-start">
              <p className="text-2xl">Clique para criar:</p>
              <p className="text-2xl font-bold">Novo relatório</p>
              <p className="text-2xl font-bold">ou pasta</p>
              <div className="justify-self-end">
                <Plus size="20%" />
              </div>
            </div>
          </AnimatedFilledButton>
        ) : null}
        {isFetched &&
          list?.length > 0 &&
          list.map((item, index) => (
            <div className="z-[1]" key={index}>
              <ReportCard report={item} />
            </div>
          ))}
      </MasonryLayout>
      {isFetched && list?.length === 0 && (
        <EmptyList onReload={() => { }} />
      )}

      {showLoadMore && loadMore && (
        <div className="w-full absolute bottom-2 left-0 flex justify-center py-4 px-8 z-[9999]">
          <Button
            variant="default"
            className="w-full uppercase"
            onClick={handleLoadMore}
          >
            Carregar mais resultados
          </Button>
        </div>
      )}
    </section>
  );
}
