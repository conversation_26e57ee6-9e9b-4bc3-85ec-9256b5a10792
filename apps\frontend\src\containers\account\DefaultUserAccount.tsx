import { Text } from "@snap/design-system";
import { Column, DataTable } from "~/components/Table";
import AccountToolbar from "./AccountToolbar";
import AccountDateFilter from "./AccountDateFilter";
import { useUserLogsList, useUserLogsTotalItems, useUserLogsFilters, useUserLogsActions } from "~/store/userLogsStore";
import { UserLogEntry } from "~/types/global";
import { REPORT_CONSTANTS, USER_CONSTANTS } from "~/helpers/constants";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const columnProps = {
  created_at: USER_CONSTANTS.user_logs.created_at,
  report_type: USER_CONSTANTS.user_logs.report_type,
  email: USER_CONSTANTS.user_logs.email,
}

const userColumns: Column<UserLogEntry>[] = [
  {
    key: columnProps.created_at,
    header: "Data da consulta",
    widthClass: "w-2/5 min-w-[280px]",
    render: (_, row) => {
      const date = new Date(row?.[columnProps.created_at as keyof typeof row]) || "NA";
      const formattedDate = date.toLocaleDateString('pt-BR');
      const formattedTime = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
      const dayOfWeek = date.toLocaleDateString('pt-BR', { weekday: 'long' });

      return (
        <div className="flex items-center gap-4">
          <Text variant="body-md" className="font-semibold">{formattedDate}</Text>
          <Text variant="body-md">{formattedTime}</Text>
          <Text className="opacity-80 capitalize">{dayOfWeek}</Text>
        </div>
      );
    },
  },
  {
    key: columnProps.report_type,
    header: "Tipo de report",
    widthClass: "w-1/4 min-w-[150px]",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row?.[columnProps.report_type as keyof typeof row]?.toUpperCase() || 'N/A'}</Text>
      </div>
    ),
  },
  {
    key: columnProps.email,
    header: "Email",
    widthClass: "w-1/3 min-w-[200px]",
    className: "overflow-hidden",
    render: (_, row) => (
      <div className="truncate">
        <Text variant="body-md">{row?.[columnProps.email as keyof typeof row] || 'N/A'}</Text>
      </div>
    ),
  }
]

const DefaultUserAccount = ({ }: UserConfigDialogContentProps) => {
  const logsList = useUserLogsList();
  const totalItems = useUserLogsTotalItems();
  const filters = useUserLogsFilters();
  const { setPage } = useUserLogsActions();

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  return (
    <div className="flex flex-col flex-4/5">
      <AccountDateFilter />
      <div>
        <AccountToolbar />
        <div className="overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-456px)]">
          <DataTable
            columns={userColumns}
            data={logsList}
            keyField={REPORT_CONSTANTS.new_report.report_id as keyof UserLogEntry}
            useFixedLayout={true}
            pagination={totalItems && totalItems > (filters.limit || 10) ? {
              pageSize: filters.limit || 10,
              totalItems: totalItems,
              currentPage: filters.page || 1,
              onPageChange: handlePageChange,
            } : undefined}
          />
        </div>
      </div>
    </div>
  );
}

export default DefaultUserAccount;