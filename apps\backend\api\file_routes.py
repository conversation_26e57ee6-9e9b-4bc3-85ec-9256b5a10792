import base64
import os
from fastapi import (APIRouter, Request, Response, HTTPException, Depends)
from kafka_consumer import send_pdf_request_and_wait
from services.minio_service import load_generated_pdf, delete_generated_pdf
from services.auth_service import auth_guard
from utils.jwt_utils import JWTUtils
import logging

logger = logging.getLogger(__name__)

DELETE_PDFS_AFTER_DOWNLOAD = os.getenv('DELETE_PDFS_AFTER_DOWNLOAD', 'true').lower() == 'true'

router = APIRouter()

@router.post("/create/pdf")
async def create_pdf(request: Request):
    try:
        data = await request.json()

        # Extract metadata from the data
        metadata = data.get('metadata', {})
        user_reports_id = metadata.get('user_reports_id')
        report_type = metadata.get('report_type')

        # Try to extract user info from cookies if available (optional)
        user_id = None
        try:
            access_token = request.cookies.get("access_token")
            if access_token:
                from services.auth_service import verify_jwt
                user_data = await verify_jwt(access_token)
                token_decoded = JWTUtils(user_jwt=user_data)
                user_id = token_decoded.get_user_id()
                logger.info("[create_pdf] Processing PDF generation request for user: %s", user_id)
        except Exception as e:
            logger.warning("[create_pdf] Could not extract user info from token: %s", str(e))
            user_id = "anonymous"

        # Add user context to data for MinIO storage
        data['user_context'] = {
            'user_id': user_id or "anonymous",
            'user_reports_id': user_reports_id,
            'report_type': report_type
        }

        # Send request and get PDF reference
        result = await send_pdf_request_and_wait(data)
        pdf_reference = result.get("pdfReference")
        filename = result.get("filename", "report.pdf")

        if not pdf_reference:
            logger.error("[create_pdf] No PDF reference received from PDF service")
            raise HTTPException(status_code=500, detail="PDF generation failed: No reference received")

        logger.info("[create_pdf] Fetching PDF from MinIO", {"pdfReference": pdf_reference, "filename": filename})

        # Fetch PDF from MinIO
        pdf_result = await load_generated_pdf(pdf_reference)
        if not pdf_result:
            logger.error("[create_pdf] PDF not found in MinIO", {"pdfReference": pdf_reference})
            raise HTTPException(status_code=404, detail="Generated PDF not found or expired")

        pdf_bytes, stored_filename = pdf_result
        final_filename = stored_filename or filename

        logger.info("[create_pdf] Streaming PDF to client", {
            "pdfReference": pdf_reference,
            "filename": final_filename,
            "sizeBytes": len(pdf_bytes)
        })

        response = Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f'attachment; filename="{final_filename}"',
                "Content-Length": str(len(pdf_bytes))
            }
        )

        # Delete PDF after successful response creation (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD:
            try:
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after successful download", {"pdfReference": pdf_reference})
            except Exception as e:
                logger.warning("[create_pdf] Failed to delete PDF after download", {"pdfReference": pdf_reference, "error": str(e)})

        return response

    except HTTPException as e:
        # Delete PDF on error if it exists (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD and 'pdf_reference' in locals():
            try:
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after error", {"pdfReference": pdf_reference})
            except Exception as del_e:
                logger.warning("[create_pdf] Failed to delete PDF after error", {"pdfReference": pdf_reference, "error": str(del_e)})
        raise
    except Exception as e:
        # Delete PDF on error if it exists (configurable)
        if DELETE_PDFS_AFTER_DOWNLOAD and 'pdf_reference' in locals():
            try:
                await delete_generated_pdf(pdf_reference)
                logger.info("[create_pdf] PDF deleted after error", {"pdfReference": pdf_reference})
            except Exception as del_e:
                logger.warning("[create_pdf] Failed to delete PDF after error", {"pdfReference": pdf_reference, "error": str(del_e)})
        logger.error("[create_pdf] Unexpected error during PDF generation", {"error": str(e)})
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")
