# apps/backend/exceptions/base_exceptions.py

from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

class ProblemDetail(Exception):
    """Exceção base seguindo RFC 7807 Problem Details para APIs HTTP"""

    def __init__(
        self,
        type_uri: str,
        title: str,
        status: int,
        detail: Optional[str] = None,
        instance: Optional[str] = None,
        errors: Optional[List[Dict[str, Any]]] = None,
        **extensions
    ):
        self.type = type_uri
        self.title = title
        self.status = status
        self.detail = detail or title
        self.instance = instance
        self.timestamp = datetime.utcnow().isoformat() + "Z"
        self.trace_id = str(uuid.uuid4())
        self.errors = errors or []
        self.extensions = extensions

        super().__init__(self.detail)

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "type": self.type,
            "title": self.title,
            "status": self.status,
            "detail": self.detail,
            "timestamp": self.timestamp,
            "trace_id": self.trace_id
        }

        if self.instance:
            result["instance"] = self.instance

        if self.errors:
            result["errors"] = self.errors

        result.update(self.extensions)
        return result

class ValidationError(ProblemDetail):
    def __init__(self, detail: str = "Falha na validação", errors: List[Dict] = None, **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/validation-error",
            title="Erro de Validação",
            status=400,
            detail=detail,
            errors=errors,
            **kwargs
        )

class AuthenticationError(ProblemDetail):
    def __init__(self, detail: str = "Falha na autenticação", **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/authentication-error",
            title="Erro de Autenticação",
            status=401,
            detail=detail,
            **kwargs
        )

class AuthorizationError(ProblemDetail):
    def __init__(self, detail: str = "Acesso negado", **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/authorization-error",
            title="Erro de Autorização",
            status=403,
            detail=detail,
            **kwargs
        )

class NotFoundError(ProblemDetail):
    def __init__(self, resource: str = "Recurso", **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/not-found-error",
            title="Recurso não encontrado",
            status=404,
            detail=f"{resource} não encontrado",
            **kwargs
        )

class BusinessLogicError(ProblemDetail):
    def __init__(self, detail: str, **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/business-logic-error",
            title="Erro de Lógica de Negócios",
            status=422,
            detail=detail,
            **kwargs
        )

class InternalServerError(ProblemDetail):
    def __init__(self, detail: str = "Erro interno do servidor", **kwargs):
        super().__init__(
            type_uri="https://api.example.com/errors/internal-server-error",
            title="Erro Interno do Servidor",
            status=500,
            detail=detail,
            **kwargs
        )
