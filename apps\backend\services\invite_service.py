import logging
import math
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import insert, select,  update, and_, asc, desc, func
from sqlalchemy.orm import selectinload
from typing import Optional

from schemas.invite_schema import InviteCreate
from schemas.user_schema import PaginatedResponse, PaginationMetadata

from core.constants import ReportTypes, InviteFields, DefaultPageInvites

from models.invite_model import Invite, InviteStatus, InviteType

from services.base_service import BaseService
from services.organization_users_service import OrganizationUsersService
from exceptions.business_exceptions import (
    UserNoActiveOrganizationError,
    InviteDatabaseOperationError,
    InviteMissingFilterError
)

logger = logging.getLogger(__name__)


class InviteService(BaseService):


    def __init__(self, db: AsyncSession, user_id: str) -> None:
        super().__init__(db)
        self.user_id=user_id
        self.organization_id=None
    

    async def create_invite(self, invite: InviteCreate) -> str:
        logger.info("[InviteService] Starting invite insertion into local DB")
        logger.info(f"[InviteService] Invite data received: {invite}")

        if invite.type_invite == InviteType.ADMINISTRADOR:
            logger.debug("[InviteService] Checking if invite type is administrador")
            report_type = [ReportTypes.cpf, ReportTypes.cnpj, ReportTypes.combinado,
                           ReportTypes.email, ReportTypes.telefone, ReportTypes.relacao]
            logger.debug(f"[InviteService] Set report_type for administrador: {report_type}")
        else:
            logger.debug("[InviteService] Invite type is not administrador, using provided report_types")
            report_type = invite.report_types
            logger.debug(f"[InviteService] Set report_type: {report_type}")

        organization_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
        logger.debug(f"[InviteService] Created OrganizationUsersService for user_id: {self.user_id}")

        user_organization_data = await organization_service.get_organization_user()
        logger.debug(f"[InviteService] user_organization_data: {user_organization_data}")

        if not user_organization_data:
            logger.warning(f"[InviteService] No active organization found for user: {self.user_id}")
            raise UserNoActiveOrganizationError(self.user_id)
        logger.debug("[InviteService] Active organization found for user")

        organization_id = user_organization_data.organization_id
        logger.debug(f"[InviteService] Using organization_id: {organization_id}")

        save_data = {
            InviteFields.user_sender_id: self.user_id,
            InviteFields.status_invite: InviteStatus.ENVIADO,
            InviteFields.report_types: report_type,
            InviteFields.sent_at: datetime.now(),
            # Fields.invite_valid_until: datetime.now() + timedelta(days=InviteParameters.invite_valid_until),
            InviteFields.organization_id: organization_id,
            InviteFields.email_invited: invite.email_invited,
            InviteFields.type_invite: invite.type_invite,
            InviteFields.credits_sent : invite.credits_sent
        }
        logger.debug(f"[InviteService] save_data prepared: {save_data}")

        try:
            query = insert(Invite).values(**save_data).returning(Invite.invite_id)
            logger.debug(f"[InviteService] Insert query: {query}")
            result = await self.db.execute(query)
            logger.debug(f"[InviteService] Insert result: {result}")
            await self.db.commit()
            logger.debug(f"[InviteService] Commit successful after insert")
            invite_id = result.scalar_one()
            logger.debug(f"[InviteService] invite_id obtained: {invite_id}")
            logger.info(
                "[InviteService] Invite created successfully with ID: %s for email: %s",
                invite_id, invite.email_invited
            )
            logger.debug(f"[InviteService] Invite commit successful for invite_id: {invite_id}")
            return invite_id

        except Exception as e:
            await self.db.rollback()
            logger.debug(f"[InviteService] Rollback performed due to exception: {e}")
            logger.error(
                "[InviteService] Failed to insert invite for email %s. Error: %s",
                invite.email_invited, str(e), exc_info=True
            )
            raise InviteDatabaseOperationError(str(e))
           

    async def get_user_invite(
        self,
        email: str = None,
        status_invite: str = None,
        type_invite: Optional[str] = None,
        invite_id: Optional[str] = None
    ):
        logger.info(f"[InviteService] get_user_invite called with email={email}, status_invite={status_invite}, type_invite={type_invite}, invite_id={invite_id}")
        logger.debug(f"[InviteService] Entered get_user_invite method")
        try:
            filters = []
            logger.debug(f"[InviteService] Initialized filters: {filters}")

            if email is not None:
                filters.append(Invite.email_invited == email)
                logger.debug(f"[InviteService] Added email filter: {email}")
            if status_invite is not None:
                filters.append(Invite.status_invite == status_invite)
                logger.debug(f"[InviteService] Added status_invite filter: {status_invite}")
            if type_invite is not None:
                filters.append(Invite.type_invite == type_invite)
                logger.debug(f"[InviteService] Added type_invite filter: {type_invite}")
            if invite_id is not None:
                filters.append(Invite.invite_id == invite_id)
                logger.debug(f"[InviteService] Added invite_id filter: {invite_id}")

            if not filters:
                logger.error(f"[InviteService] No filters passed to get_user_invite for user {self.user_id}")
                raise InviteMissingFilterError("Pelo menos um filtro (email, status_invite, type_invite ou invite_id) deve ser fornecido para buscar um convite.")
            logger.debug(f"[InviteService] Final filters: {filters}")

            invites_query = (
                select(Invite)
                .options(selectinload(Invite.user_sender), selectinload(Invite.organization))
                .where(and_(*filters))
                .order_by(Invite.sent_at.desc())
            )
            logger.info(f"[InviteService] get_user_invite query: {invites_query}")

            invites_result = await self.db.execute(invites_query)
            logger.debug(f"[InviteService] get_user_invite result: {invites_result}")
            invites = invites_result.scalars().all()
            logger.debug(f"[InviteService] invites list: {invites}")
            logger.info("[InviteService] Found %d invites for email: %s", len(invites) if invites else 0, email)
            logger.debug(f"[InviteService] Invites found: {invites}")

            invite_list = []
            logger.info(f"[InviteService] Initialized invite_list: {invite_list}")
            for invite in invites:
                logger.info(f"[InviteService] Processing invite: {invite}")
                
                invite_dict = {
                    InviteFields.invite_id: str(invite.invite_id),
                    InviteFields.email_invited: invite.email_invited,
                    InviteFields.status_invite: invite.status_invite,
                    InviteFields.user_sender_id: str(invite.user_sender.user_id),
                    InviteFields.name_sender: invite.user_sender.name,
                    InviteFields.email_sender: invite.user_sender.email,
                    InviteFields.organization_name: invite.organization.name if invite.organization else None
                }
                logger.debug(f"[InviteService] invite_dict created: {invite_dict}")
                invite_list.append(invite_dict)
                logger.debug(f"[InviteService] invite_list updated: {invite_list}")
            logger.info(f"[InviteService] Returning invite_list: {invite_list}")
            return invite_list

        except Exception as e:
            logger.error(
                "[InviteService] Failed to get invites for email %s. Error: %s",
                email, str(e), exc_info=True
            )
            raise InviteDatabaseOperationError(str(e))


    async def get_organizations_invite(
        self,
        status_invite: str = None,
        type_invite: str = None,
        specific_date: datetime = None,
        order: str = "desc",
        column_order: str = "sent_at",
        limit: int = DefaultPageInvites.pagedefault,
        page: int = 1
    ):
        logger.info(f"[InviteService] get_organizations_invite called with status_invite={status_invite}, type_invite={type_invite}, specific_date={specific_date}, order={order}, column_order={column_order}, limit={limit}, page={page}")
        logger.info(f"[InviteService] Entered get_organizations_invite method")
        logger.info(f"[InviteService] Organization ID: {self.organization_id}")
        try:
            filters = [Invite.organization_id == self.organization_id]
            logger.info(f"[InviteService] Initialized filters: {filters}")

            if status_invite is not None:
                filters.append(Invite.status_invite == status_invite)
                logger.info(f"[InviteService] Added status_invite filter: {status_invite}")
            if type_invite is not None:
                filters.append(Invite.type_invite == type_invite)
                logger.info(f"[InviteService] Added type_invite filter: {type_invite}")
            if specific_date is not None:
                filters.append(Invite.sent_at==specific_date)
                logger.info(f"[InviteService] Added specific_date filter: {specific_date}")

            logger.info(f"[InviteService] Final filters: {filters}")

            # Base query for data
            base_query = select(Invite).where(and_(*filters))
            # Count query for total items
            count_query = select(func.count(Invite.invite_id)).where(and_(*filters))

            page = max(1, page)
            logger.info(f"[InviteService] page set to: {page}")
            offset = (page - 1) * limit if limit else None
            logger.info(f"[InviteService] Pagination - page: {page}, limit: {limit}, offset: {offset}")

            column_map = {
                "sent_at": Invite.sent_at,
                "email_invited": Invite.email_invited
            }
            logger.info(f"[InviteService] column_map: {column_map}")

            if column_order not in column_map:
                logger.warning("[InviteService][user_id(%s)] Invalid column_order: '%s', defaulting to 'sent_at'",self.user_id, column_order)

            sort_column = column_map.get(column_order, Invite.sent_at)
            logger.info(f"[InviteService] sort_column: {sort_column}")

            sort_func = asc if order.lower() == "asc" else desc
            logger.info(f"[InviteService] sort_func: {sort_func}")

            base_query = base_query.order_by(sort_func(sort_column))

            if limit:
                logger.info(f"[InviteService] Applying limit: {limit} and offset: {offset}")
                base_query = base_query.limit(limit)
                if offset:
                    base_query = base_query.offset(offset)
                logger.info("[InviteService][user(%s)] Limit: %s | Page: %s | Offset: %s" ,self.user_id, limit, page, offset)

            # Execute both queries
            invites_result = await self.db.execute(base_query)
            logger.info(f"[InviteService] get_organizations_invite result: {invites_result}")

            invites =  invites_result.mappings().all()
            def invite_to_dict(invite):
                return {
                    "invite_id": str(invite.invite_id),
                    "user_sender_id": str(invite.user_sender_id),
                    "organization_id": str(invite.organization_id),
                    "status_invite": invite.status_invite,
                    "type_invite": invite.type_invite,
                    "report_types": invite.report_types,
                    "sent_at": invite.sent_at.isoformat() if invite.sent_at else None,
                    "email_invited": invite.email_invited,
                    "credits_sent": invite.credits_sent,
                }

            data = [invite_to_dict(row['Invite']) for row in invites]

            logger.info(f"[InviteService] invites list: {invites}")
            logger.info("[InviteService] Invite details: %s", invites)
            logger.info(f"[InviteService] Returning invites as list of dicts")

            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )

            logger.info("[InviteService] Found %d invites for organization: %s. Total: %d, Page: %d/%d",
                       len(invites), self.organization_id, total_items, page, total_pages)

            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logger.error(
                "[InviteService] Failed to get invites for organization %s. Error: %s",
                self.organization_id, str(e), exc_info=True
            )
            raise InviteDatabaseOperationError(str(e))


    async def update_invite_status(
        self,
        new_status: str,
        email_invited: str = None,
        invite_id: str = None
    ):
        logger.info(
            "[InviteService] Updating invite status for email: %s, organization: %s, invite_id: %s to status: %s",
            email_invited, self.organization_id, invite_id, new_status
        )
        logger.debug(f"[InviteService] update_invite_status called with new_status={new_status}, email_invited={email_invited}, invite_id={invite_id}, organization_id={self.organization_id}")
        try:
            filters = []
            logger.debug(f"[InviteService] Initialized filters: {filters}")
            if email_invited is not None:
                filters.append(Invite.email_invited == email_invited)
                logger.debug(f"[InviteService] Added email_invited filter: {email_invited}")
            if self.organization_id is not None:
                filters.append(Invite.organization_id == self.organization_id)
                logger.debug(f"[InviteService] Added organization_id filter: {self.organization_id}")
            if invite_id is not None:
                filters.append(Invite.invite_id == invite_id)
                logger.debug(f"[InviteService] Added invite_id filter: {invite_id}")
            
            if not filters:
                logger.error("[InviteService] No filters specified for update_invite_status. Aborting to prevent mass update!")
                raise InviteMissingFilterError("Pelo menos um filtro (email_invited, organization_id ou invite_id) deve ser fornecido para atualizar convite.")
            logger.debug(f"[InviteService] Final filters: {filters}")

            result = await self.db.execute(
                update(Invite)
                .where(and_(*filters))
                .values(status_invite=new_status)
            )
            logger.debug(f"[InviteService] update_invite_status DB result: {result}")
            await self.db.commit()
            logger.debug(f"[InviteService] update_invite_status commit successful")

            success = result.rowcount > 0
            logger.debug(f"[InviteService] update_invite_status success: {success}")
            if success:
                logger.info("[InviteService] Successfully updated invite status")
                # Fetch the updated row
                select_query = select(Invite).where(and_(*filters))
                updated_result = await self.db.execute(select_query)
                updated_invite = updated_result.scalar_one_or_none()
                if updated_invite:
                    # Convert to dict (customize as needed)
                    invite_dict = {
                        'invite_id': str(updated_invite.invite_id),
                        'user_sender_id': str(updated_invite.user_sender_id),
                        'organization_id': str(updated_invite.organization_id),
                        'status_invite': updated_invite.status_invite,
                        'type_invite': updated_invite.type_invite,
                        'report_types': updated_invite.report_types,
                        'sent_at': updated_invite.sent_at.isoformat() if updated_invite.sent_at else None,
                        'email_invited': updated_invite.email_invited,
                        'credits_sent': updated_invite.credits_sent,
                    }
                    return invite_dict
                else:
                    logger.warning(f"[InviteService] Updated invite not found after update with filters: {filters}")
                    return None
            else:
                logger.warning(f"[InviteService] No invite found to update with given filters: {filters}")
                return None

        except Exception as e:
            await self.db.rollback()
            logger.debug(f"[InviteService] Rollback performed due to exception: {e}")
            logger.error(
                "[InviteService] Failed to update invite status. Error: %s",
                str(e), exc_info=True
            )
            raise InviteDatabaseOperationError(str(e))