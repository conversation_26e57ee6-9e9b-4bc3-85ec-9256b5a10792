import dotenv from 'dotenv';
import path from 'path';
import puppeteer, { <PERSON><PERSON><PERSON> } from "puppeteer";
import { initializeServer } from './server';
import serverConfig from './config/server';
import * as logger from './utils/logger';

dotenv.config({ path: path.resolve(__dirname, '../.env') });

let browser: Browser;

/**
 * Initialize the Puppeteer browser and start both HTTP server and Kafka consumer
 */
async function main() {
  try {
    logger.info('Initializing browser...');
    browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const version = await browser.version();
    logger.info(`<PERSON><PERSON><PERSON> initialized successfully`, { version });

    // Start HTTP server
    initializeServer(browser, serverConfig);

    // Start Kafka consumer if enabled
    const enableKafka = process.env.ENABLE_KAFKA_CONSUMER !== 'false';
    if (enableKafka) {
      // TODO - dados transferidos não podem ficar armazenados no kafka
      logger.info('Starting Kafka consumer...');
      const { startKafkaConsumer } = await import('./service/kafka/pdfConsumer');
      await startKafkaConsumer();
    }

  } catch (err) {
    logger.error('Failed to initialize browser or start server', { error: err });
    process.exit(1);
  }
}

main()
  .catch((err) => {
    logger.error('Unhandled error in main function', err);
    process.exit(1);
  });

process.on('SIGINT', async () => {
  logger.info('Received SIGINT signal, shutting down...');
  if (browser) {
    await browser.close();
    logger.info('Browser closed successfully');
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM signal, shutting down...');
  if (browser) {
    await browser.close();
    logger.info('Browser closed successfully');
  }
  process.exit(0);
});
