import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { isBase64Image, isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { GridItem } from "@snap/design-system";
import { ValidatedImage } from "../components/ValidateImage";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import { PossivelPessoaRelacionada } from "../../model/PossiveisPessoasRelacionadas";

export function useRenderPossiveisPessoasRelacionadas(
  sectionTitle: string
): ArrayRenderStrategy<PossivelPessoaRelacionada> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<PossivelPessoaRelacionada, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  // Test functions for individual keys (following NEW_renderParentes.strategy.tsx pattern)
  const testNomeCompletoDeleted = (e: any) => e.nome_completo?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted === true)
      : true;
  const testTelefonesDeleted = (e: any) =>
    e.telefones
      ? e.telefones.every((t: any) =>
        Object.values(t.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEmailsDeleted = (e: any) =>
    e.emails
      ? e.emails.every((email: any) =>
        Object.values(email.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testEnderecosDeleted = (e: any) =>
    e.enderecos
      ? e.enderecos.every((end: any) =>
        Object.values(end.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testImagensDeleted = (e: any) =>
    e.imagens
      ? e.imagens.every((img: any) =>
        Object.values(img.value || {}).every((v: any) => v.is_deleted === true)
      )
      : true;
  const testRedesSociaisDeleted = (e: any) =>
    e.redes_sociais
      ? e.redes_sociais.every((redesEntry: any) =>
        redesEntry.detalhes
          ? redesEntry.detalhes.every((detalhe: any) => {
            if (detalhe.value) {
              return Object.values(detalhe.value).every((platform: any) => {
                if (Array.isArray(platform)) {
                  return platform.every((profile: any) =>
                    Object.values(profile).every((field: any) => field.is_deleted === true)
                  );
                }
                return true;
              });
            }
            return detalhe.is_deleted === true;
          })
          : true
      )
      : true;

  // Test function for generic lists (following participantKeys pattern from NEW_renderProcessos.strategy.tsx)
  const testGenericListsDeleted = (e: any) => {
    const genericListKeys = Object.keys(e).filter(key =>
      key !== 'nome_completo' &&
      key !== 'detalhes' &&
      key !== 'telefones' &&
      key !== 'emails' &&
      key !== 'enderecos' &&
      key !== 'imagens' &&
      key !== 'redes_sociais' &&
      Array.isArray(e[key])
    );

    if (genericListKeys.length === 0) return true;

    return genericListKeys.every(key => {
      const list = e[key] as any[];
      return list.every((item: any) => {
        if (item.value && typeof item.value === 'object') {
          return Object.values(item.value).every((field: any) => field.is_deleted === true);
        }
        return item.is_deleted === true;
      });
    });
  };

  const testEntryDeleted = (entry: any): boolean => {
    const isNomeCompletoDeleted = testNomeCompletoDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const areTelefonesDeleted = testTelefonesDeleted(entry);
    const areEmailsDeleted = testEmailsDeleted(entry);
    const areEnderecosDeleted = testEnderecosDeleted(entry);
    const areImagensDeleted = testImagensDeleted(entry);
    const areRedesSociaisDeleted = testRedesSociaisDeleted(entry);
    const areGenericListsDeleted = testGenericListsDeleted(entry);

    return isNomeCompletoDeleted && areDetalhesDeleted && areTelefonesDeleted && areEnderecosDeleted && areImagensDeleted && areRedesSociaisDeleted && areEmailsDeleted && areGenericListsDeleted;
  };

  const testSectionDeleted = (section: any): boolean => {
    if (!section?.data?.length) return true;
    return section.data.every((entry: any) => testEntryDeleted(entry));
  };

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  // Helper functions for nested arrays (following NEW_renderParentes.strategy.tsx pattern)
  const shouldIncludeNestedBlock = (item: any) => {
    const vals = Object.values(item.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const shouldIncludeList = (arrayItems: any[]) => {
    if (!Array.isArray(arrayItems)) return false;
    return arrayItems.some((item: any) => shouldIncludeNestedBlock(item));
  };

  // Toggle functions for different property types
  const onToggleDetalhesField = (idx: number, detalheIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        if (entry.detalhes?.[fieldKey]) {
          entry.detalhes[fieldKey].is_deleted = !entry.detalhes[fieldKey].is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleTelefonesField = (idx: number, telefoneIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const telefone = entry.telefones?.[telefoneIdx];
        if (telefone?.value?.[fieldKey]) {
          telefone.value[fieldKey].is_deleted = !telefone.value[fieldKey].is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleEmailsField = (idx: number, emailIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const email = entry.emails?.[emailIdx];
        if (email?.value?.[fieldKey]) {
          email.value[fieldKey].is_deleted = !email.value[fieldKey].is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleEnderecosField = (idx: number, enderecoIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const endereco = entry.enderecos?.[enderecoIdx];
        if (endereco?.value?.[fieldKey]) {
          endereco.value[fieldKey].is_deleted = !endereco.value[fieldKey].is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleImagensField = (idx: number, imagemIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const imagem = entry.imagens?.[imagemIdx];
        if (imagem?.value?.[fieldKey]) {
          imagem.value[fieldKey].is_deleted = !imagem.value[fieldKey].is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  // Redes sociais toggle functions (copied from NEW_renderRedesSociais.strategy.tsx)
  const shouldIncludePlatform = (profiles: any[]) => {
    return profiles.some(profile =>
      Object.values(profile).some((field: any) => includeKey(field.is_deleted))
    );
  };

  const shouldIncludeProfile = (profile: any) => {
    return Object.values(profile).some((field: any) => includeKey(field.is_deleted));
  };

  const onTogglePlatform = (idx: number, detalheIdx: number, platform: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const redesEntry = entry.redes_sociais?.[0]; // Assuming single redes_sociais entry
        const detalhe = redesEntry?.detalhes?.[detalheIdx];
        if (detalhe?.value?.[platform]) {
          const profiles = detalhe.value[platform];
          const hasAnyDeleted = profiles.some((profile: any) =>
            Object.values(profile).some((field: any) => field.is_deleted)
          );

          // Toggle all profiles in this platform
          profiles.forEach((profile: any) => {
            Object.values(profile).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = !hasAnyDeleted;
              }
            });
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleProfile = (idx: number, detalheIdx: number, platform: string, profileIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const redesEntry = entry.redes_sociais?.[0];
        const profile = redesEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx];
        if (profile) {
          const hasAnyDeleted = Object.values(profile).some((field: any) => field.is_deleted);

          Object.values(profile).forEach((field: any) => {
            if (field && typeof field === 'object' && 'is_deleted' in field) {
              field.is_deleted = !hasAnyDeleted;
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onTogglePlatformField = (idx: number, detalheIdx: number, platform: string, profileIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const redesEntry = entry.redes_sociais?.[0];
        const field = redesEntry?.detalhes?.[detalheIdx]?.value?.[platform]?.[profileIdx]?.[fieldKey];
        if (field) {
          field.is_deleted = !field.is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  // Toggle functions for generic lists (following participantKeys pattern from NEW_renderProcessos.strategy.tsx)
  const onToggleGenericListTitle = (idx: number, listKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const list = entry[listKey] as any[];
        if (Array.isArray(list)) {
          const hasAnyDeleted = list.some((item: any) => {
            if (item.value && typeof item.value === 'object') {
              return Object.values(item.value).some((field: any) => field.is_deleted);
            }
            return item.is_deleted;
          });

          // Toggle all items in this list
          list.forEach((item: any) => {
            item.is_deleted = !hasAnyDeleted;
            if (item.value && typeof item.value === 'object') {
              Object.values(item.value).forEach((field: any) => {
                if (field && typeof field === 'object' && 'is_deleted' in field) {
                  field.is_deleted = !hasAnyDeleted;
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListItem = (idx: number, listKey: string, itemIdx: number) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const item = entry[listKey]?.[itemIdx];
        if (item) {
          if (item.value && typeof item.value === 'object') {
            const hasAnyDeleted = Object.values(item.value).some((field: any) => field.is_deleted);

            Object.values(item.value).forEach((field: any) => {
              if (field && typeof field === 'object' && 'is_deleted' in field) {
                field.is_deleted = !hasAnyDeleted;
              }
            });

            // Update item's is_deleted based on all fields
            const allFieldsDeleted = Object.values(item.value).every((field: any) => field.is_deleted === true);
            item.is_deleted = allFieldsDeleted;
          } else {
            item.is_deleted = !item.is_deleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleGenericListField = (idx: number, listKey: string, itemIdx: number, fieldKey: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    updater(
      sectionTitle,
      entry => {
        const field = entry[listKey]?.[itemIdx]?.value?.[fieldKey];
        if (field) {
          field.is_deleted = !field.is_deleted;

          // Update item's is_deleted based on all fields
          const item = entry[listKey]?.[itemIdx];
          if (item?.value) {
            const allFieldsDeleted = Object.values(item.value).every((f: any) => f.is_deleted === true);
            item.is_deleted = allFieldsDeleted;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (entry?: PossivelPessoaRelacionada) => React.ReactElement | null
  > = {
    nome_completo: (entry) => {
      if (!entry?.nome_completo || !includeKey(entry.nome_completo.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={3} key={`nome-completo-${idx}`}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx && e.nome_completo) {
                    e.nome_completo.is_deleted = !e.nome_completo.is_deleted;

                    // Cascading deletion logic: when nome_completo is deleted, delete all other fields
                    // When nome_completo is restored, restore all other fields
                    const targetDeletedState = e.nome_completo.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((campo: any) => {
                        if (campo) campo.is_deleted = targetDeletedState;
                      });
                    }

                    // Apply to telefones array
                    if (e.telefones) {
                      e.telefones.forEach((t: any) => {
                        t.is_deleted = targetDeletedState;
                        if (t.value) {
                          Object.values(t.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    if (e.emails) {
                      e.emails.forEach((email: any) => {
                        email.is_deleted = targetDeletedState;
                        if (email.value) {
                          Object.values(email.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to enderecos array
                    if (e.enderecos) {
                      e.enderecos.forEach((end: any) => {
                        end.is_deleted = targetDeletedState;
                        if (end.value) {
                          Object.values(end.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to imagens array
                    if (e.imagens) {
                      e.imagens.forEach((img: any) => {
                        img.is_deleted = targetDeletedState;
                        if (img.value) {
                          Object.values(img.value).forEach((campo: any) => {
                            if (campo) campo.is_deleted = targetDeletedState;
                          });
                        }
                      });
                    }

                    // Apply to redes_sociais array
                    if (e.redes_sociais) {
                      e.redes_sociais.forEach((redesEntry: any) => {
                        if (redesEntry.detalhes) {
                          redesEntry.detalhes.forEach((detalhe: any) => {
                            detalhe.is_deleted = targetDeletedState;
                            if (detalhe.value) {
                              Object.values(detalhe.value).forEach((platform: any) => {
                                if (Array.isArray(platform)) {
                                  platform.forEach((profile: any) => {
                                    Object.values(profile).forEach((field: any) => {
                                      if (field) field.is_deleted = targetDeletedState;
                                    });
                                  });
                                }
                              });
                            }
                          });
                        }
                      });
                    }

                    // Apply to generic lists (following participantKeys pattern from NEW_renderProcessos.strategy.tsx)
                    const genericListKeys = Object.keys(e).filter(key =>
                      key !== 'nome_completo' &&
                      key !== 'detalhes' &&
                      key !== 'telefones' &&
                      key !== 'emails' &&
                      key !== 'enderecos' &&
                      key !== 'imagens' &&
                      key !== 'redes_sociais' &&
                      Array.isArray(e[key])
                    );

                    genericListKeys.forEach(key => {
                      if (Array.isArray(e[key])) {
                        e[key].forEach((item: any) => {
                          item.is_deleted = targetDeletedState;
                          if (item.value && typeof item.value === 'object') {
                            Object.keys(item.value).forEach(fieldKey => {
                              if (item.value[fieldKey]) {
                                item.value[fieldKey].is_deleted = targetDeletedState;
                              }
                            });
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.nome_completo.label.toUpperCase()}
              colorClass="bg-primary"
              labelTextClass="text-accent"
              value={parseValue(entry.nome_completo.value)}
              tooltip={renderSourceTooltip(entry.nome_completo.source)}
              className="border-0 pb-0"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                value={parseValue(String((val as any).value))}
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    telefones: (entry) => {
      if (!entry?.telefones?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredTelefones = entry.telefones.filter((telefone: any) => {
        if (telefone.value) {
          return Object.values(telefone.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(telefone.is_deleted);
      });

      if (filteredTelefones.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`telefones-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all telefones
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              const hasAnyDeleted = entry.telefones?.some((telefone: any) => {
                if (telefone.value) {
                  return Object.values(telefone.value).some((field: any) => field.is_deleted);
                }
                return telefone.is_deleted;
              });

              updater(
                sectionTitle,
                entryToUpdate => {
                  if (entryToUpdate.telefones) {
                    entryToUpdate.telefones.forEach((telefone: any) => {
                      if (telefone.value) {
                        Object.values(telefone.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = !hasAnyDeleted;
                          }
                        });
                      }
                      if (telefone && typeof telefone === 'object' && 'is_deleted' in telefone) {
                        telefone.is_deleted = !hasAnyDeleted;
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="TELEFONES"
              colorClass="bg-white"
              labelTextClass="text-white"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {filteredTelefones.map((telefone: any, renderIdx: number) => {
              const originalIdx = entry.telefones!.indexOf(telefone);

              if (telefone.value) {
                return (
                  <GridItem key={`tel-${idx}-${originalIdx}`} cols={1}>
                    <CustomGridItem
                      cols={1}
                      containerClassName="w-fit pr-12"
                      onToggleField={() => {
                        // Toggle this specific telefone
                        const updater = actions.updateSectionEntries;
                        if (!updater) return;

                        const hasAnyDeleted = Object.values(telefone.value).some((field: any) => field.is_deleted);

                        updater(
                          sectionTitle,
                          entryToUpdate => {
                            const telefoneToUpdate = entryToUpdate.telefones?.[originalIdx];
                            if (telefoneToUpdate?.value) {
                              Object.values(telefoneToUpdate.value).forEach((field: any) => {
                                if (field && typeof field === 'object' && 'is_deleted' in field) {
                                  field.is_deleted = !hasAnyDeleted;
                                }
                              });
                            }
                          },
                          testEntryDeleted as any,
                          testSectionDeleted,
                          calculateDataCount
                        );
                      }}
                    >
                      <ReportsCustomLabel
                        label={`TELEFONE ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      {Object.entries(telefone.value)
                        .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                        .map(([fieldKey, fieldValue]: any) => (
                          <CustomGridItem
                            key={`telefone-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleTelefonesField(idx, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={String(fieldValue.value)}
                              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        ))}
                    </div>
                  </GridItem>
                );
              }
              return null;
            })}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    emails: (entry) => {
      if (!entry?.emails?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredEmails = entry.emails.filter((email: any) => {
        if (email.value) {
          return Object.values(email.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(email.is_deleted);
      });

      if (filteredEmails.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`emails-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all emails
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              const hasAnyDeleted = entry.emails?.some((email: any) => {
                if (email.value) {
                  return Object.values(email.value).some((field: any) => field.is_deleted);
                }
                return email.is_deleted;
              });

              updater(
                sectionTitle,
                entryToUpdate => {
                  if (entryToUpdate.emails) {
                    entryToUpdate.emails.forEach((email: any) => {
                      if (email.value) {
                        Object.values(email.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = !hasAnyDeleted;
                          }
                        });
                      }
                      if (email && typeof email === 'object' && 'is_deleted' in email) {
                        email.is_deleted = !hasAnyDeleted;
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="EMAILS"
              colorClass="bg-white"
              labelTextClass="text-white"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {filteredEmails.map((email: any, renderIdx: number) => {
              const originalIdx = entry.emails!.indexOf(email);

              if (email.value) {
                return (
                  <GridItem key={`email-${idx}-${originalIdx}`} cols={1}>
                    <CustomGridItem
                      cols={1}
                      containerClassName="w-fit pr-12"
                      onToggleField={() => {
                        const updater = actions.updateSectionEntries;
                        if (!updater) return;

                        const hasAnyDeleted = Object.values(email.value).some((field: any) => field.is_deleted);

                        updater(
                          sectionTitle,
                          entryToUpdate => {
                            const emailToUpdate = entryToUpdate.emails?.[originalIdx];
                            if (emailToUpdate?.value) {
                              Object.values(emailToUpdate.value).forEach((field: any) => {
                                if (field && typeof field === 'object' && 'is_deleted' in field) {
                                  field.is_deleted = !hasAnyDeleted;
                                }
                              });
                            }
                          },
                          testEntryDeleted as any,
                          testSectionDeleted,
                          calculateDataCount
                        );
                      }}
                    >
                      <ReportsCustomLabel
                        label={`EMAIL ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      {Object.entries(email.value)
                        .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                        .map(([fieldKey, fieldValue]: any) => (
                          <CustomGridItem
                            key={`email-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleEmailsField(idx, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={String(fieldValue.value)}
                              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        ))}
                    </div>
                  </GridItem>
                );
              }
              return null;
            })}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    enderecos: (entry) => {
      if (!entry?.enderecos?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredEnderecos = entry.enderecos.filter((endereco: any) => {
        if (endereco.value) {
          return Object.values(endereco.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(endereco.is_deleted);
      });

      if (filteredEnderecos.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`enderecos-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all enderecos
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              const hasAnyDeleted = entry.enderecos?.some((endereco: any) => {
                if (endereco.value) {
                  return Object.values(endereco.value).some((field: any) => field.is_deleted);
                }
                return endereco.is_deleted;
              });

              updater(
                sectionTitle,
                entryToUpdate => {
                  if (entryToUpdate.enderecos) {
                    entryToUpdate.enderecos.forEach((endereco: any) => {
                      if (endereco.value) {
                        Object.values(endereco.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = !hasAnyDeleted;
                          }
                        });
                      }
                      if (endereco && typeof endereco === 'object' && 'is_deleted' in endereco) {
                        endereco.is_deleted = !hasAnyDeleted;
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="ENDEREÇOS"
              colorClass="bg-white"
              labelTextClass="text-white"
            />
          </CustomGridItem>

          <CustomGridContainer cols={2}>
            {filteredEnderecos.map((endereco: any, renderIdx: number) => {
              const originalIdx = entry.enderecos!.indexOf(endereco);

              if (endereco.value) {
                return (
                  <GridItem key={`end-${idx}-${originalIdx}`} cols={1}>
                    <CustomGridItem
                      cols={1}
                      containerClassName="w-fit pr-12"
                      onToggleField={() => {
                        // Toggle this specific endereco
                        const updater = actions.updateSectionEntries;
                        if (!updater) return;

                        const hasAnyDeleted = Object.values(endereco.value).some((field: any) => field.is_deleted);

                        updater(
                          sectionTitle,
                          entryToUpdate => {
                            const enderecoToUpdate = entryToUpdate.enderecos?.[originalIdx];
                            if (enderecoToUpdate?.value) {
                              Object.values(enderecoToUpdate.value).forEach((field: any) => {
                                if (field && typeof field === 'object' && 'is_deleted' in field) {
                                  field.is_deleted = !hasAnyDeleted;
                                }
                              });
                            }
                          },
                          testEntryDeleted as any,
                          testSectionDeleted,
                          calculateDataCount
                        );
                      }}
                    >
                      <ReportsCustomLabel
                        label={`ENDEREÇO ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      {Object.entries(endereco.value)
                        .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                        .map(([fieldKey, fieldValue]: any) => (
                          <CustomGridItem
                            key={`endereco-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleEnderecosField(idx, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={String(fieldValue.value)}
                              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        ))}
                    </div>
                  </GridItem>
                );
              }
              return null;
            })}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    imagens: (entry) => {
      if (!entry?.imagens?.length) return null;
      const idx = idxMap.get(entry)!;

      const filteredImagens = entry.imagens.filter((imagem: any) => {
        if (imagem.value) {
          return Object.values(imagem.value).some((field: any) => includeKey(field.is_deleted));
        }
        return includeKey(imagem.is_deleted);
      });

      if (filteredImagens.length === 0) return null;

      return (
        <CustomGridContainer cols={1} key={`imagens-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all imagens
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              const hasAnyDeleted = entry.imagens?.some((imagem: any) => {
                if (imagem.value) {
                  return Object.values(imagem.value).some((field: any) => field.is_deleted);
                }
                return imagem.is_deleted;
              });

              updater(
                sectionTitle,
                entryToUpdate => {
                  if (entryToUpdate.imagens) {
                    entryToUpdate.imagens.forEach((imagem: any) => {
                      if (imagem.value) {
                        Object.values(imagem.value).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = !hasAnyDeleted;
                          }
                        });
                      }
                      if (imagem && typeof imagem === 'object' && 'is_deleted' in imagem) {
                        imagem.is_deleted = !hasAnyDeleted;
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="IMAGENS"
              colorClass="bg-white"
              labelTextClass="text-white"
            />
          </CustomGridItem>

          <CustomGridContainer cols={4}>
            {(() => {
              let imageCounter = 1;

              return filteredImagens.map((imagem: any) => {
                const originalIdx = entry.imagens!.indexOf(imagem);

                if (imagem.value) {
                  return Object.entries(imagem.value)
                    .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                    .map(([fieldKey, fieldValue]: any) => {
                      const currentImageNumber = imageCounter++;

                      return (
                        <CustomGridItem
                          key={`imagem-${originalIdx}-${fieldKey}`}
                          cols={1}
                          onToggleField={() => onToggleImagensField(idx, originalIdx, fieldKey)}
                        >
                          <div className="py-2 group">
                            <CustomReadOnlyInputField
                              label={`IMAGEM ${currentImageNumber}`}
                              value={String(fieldValue.value || '')}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                            <ValidatedImage
                              src={String(fieldValue.value)}
                              alt={`Imagem ${currentImageNumber}`}
                              className="w-full max-w-full h-48 mx-auto mt-2 bg-background/40 rounded-sm"
                            />
                          </div>
                        </CustomGridItem>
                      );
                    });
                }
                return null;
              });
            })()}
          </CustomGridContainer>
        </CustomGridContainer>
      );
    },

    redes_sociais: (entry) => {
      if (!entry?.redes_sociais?.length) return null;
      const idx = idxMap.get(entry)!;

      const redesEntry = entry.redes_sociais[0]; // Assuming single redes_sociais entry
      if (!redesEntry?.detalhes?.length) return null;

      const platformElements: React.ReactElement[] = [];

      // First, collect all platform elements
      redesEntry.detalhes.forEach((detalhe: any, detalheIdx: number) => {
        if (!detalhe.value) return;

        // Process each platform in this detalhe
        Object.entries(detalhe.value).forEach(([platform, profiles]) => {
          if (!Array.isArray(profiles) || !shouldIncludePlatform(profiles)) return;

          // Format platform name for display
          let platformName = platform.charAt(0).toUpperCase() + platform.slice(1);
          if (platform === "x (twitter)") platformName = "X (Twitter)";

          // Filter profiles to show
          const filteredProfiles = profiles
            .map((profile: any, profileIdx: number) => ({ profile, originalIdx: profileIdx }))
            .filter(({ profile }) => shouldIncludeProfile(profile));

          if (filteredProfiles.length === 0) return;

          platformElements.push(
            <CustomGridContainer cols={1} key={`platform-${platform}-${detalheIdx}`} className="mb-6">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pr-12"
                onToggleField={() => onTogglePlatform(idx, detalheIdx, platform)}
              >
                <ReportsCustomLabel
                  label={platformName.toUpperCase()}
                  colorClass="bg-white"
                  labelTextClass="text-white"
                />
              </CustomGridItem>

              <CustomGridContainer cols={2}>
                {filteredProfiles.map(({ profile, originalIdx }, renderIdx) => (
                  <GridItem key={`${platform}-profile-${originalIdx}`} cols={1} >
                    <CustomGridItem
                      cols={1}
                      className="py-1"
                      containerClassName="w-fit pr-12"
                      onToggleField={() => onToggleProfile(idx, detalheIdx, platform, originalIdx)}
                    >
                      <ReportsCustomLabel
                        label={`${platformName.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </CustomGridItem>
                    <div className="pl-5">
                      {Object.entries(profile)
                        .filter(([_, fieldValue]: any) =>
                          isTrash ? fieldValue.is_deleted : !fieldValue.is_deleted
                        )
                        .map(([fieldKey, fieldValue]: any) => {
                          const isImageValue = !Array.isArray(fieldValue.value) &&
                            (isValidUrl(fieldValue.value) || isBase64Image(fieldValue.value));
                          if (isImageValue) {
                            return (
                              <CustomGridItem
                                key={`${platform}-${originalIdx}-${fieldKey}`}
                                cols={1}
                                className="py-1"
                                onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                              >
                                <div className="py-2 group">
                                  <CustomReadOnlyInputField
                                    label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                    value={String(fieldValue.value)}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(fieldValue.source)}
                                  />
                                  <ValidatedImage
                                    src={String(fieldValue.value)}
                                    alt={`Imagem ${renderIdx + 1}`}
                                    className="w-full max-w-full h-48 mx-auto mt-2 bg-background/40 rounded-sm"
                                  />
                                </div>
                              </CustomGridItem>
                            );
                          }

                          // Handle array values (like vinculo)
                          if (Array.isArray(fieldValue.value)) {
                            const displayValue = fieldValue.value
                              .map((item: any) => item.rotulo || item)
                              .join(", ");

                            return (
                              <CustomGridItem
                                key={`${platform}-${originalIdx}-${fieldKey}`}
                                cols={1}
                                className="py-1"
                                onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                              >
                                <CustomReadOnlyInputField
                                  label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                  value={displayValue}
                                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                  tooltip={renderSourceTooltip(fieldValue.source)}
                                />
                              </CustomGridItem>
                            );
                          }

                          // Handle regular string values
                          return (
                            <CustomGridItem
                              key={`${platform}-${originalIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => onTogglePlatformField(idx, detalheIdx, platform, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={String(fieldValue.value)}
                                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          );
                        })}
                    </div>
                  </GridItem>
                ))}
              </CustomGridContainer>
            </CustomGridContainer>
          );
        });
      });

      // Only show the section if there are platform elements to display
      if (platformElements.length === 0) return null;

      const allElements: React.ReactElement[] = [];

      // Add main title for Redes Sociais section
      allElements.push(
        <CustomGridContainer cols={1} key={`redes-sociais-title-${idx}`} className="mb-6">
          <CustomGridItem
            cols={1}
            containerClassName="w-fit pr-12"
            onToggleField={() => {
              // Toggle all redes_sociais
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              const hasAnyDeleted = entry.redes_sociais?.some((redesEntry: any) => {
                return redesEntry.detalhes?.some((detalhe: any) => {
                  if (detalhe.value) {
                    return Object.values(detalhe.value).some((platform: any) => {
                      if (Array.isArray(platform)) {
                        return platform.some((profile: any) => {
                          return Object.values(profile).some((field: any) => field.is_deleted);
                        });
                      }
                      return false;
                    });
                  }
                  return detalhe.is_deleted;
                });
              });

              updater(
                sectionTitle,
                entryToUpdate => {
                  if (entryToUpdate.redes_sociais) {
                    entryToUpdate.redes_sociais.forEach((redesEntry: any) => {
                      if (redesEntry.detalhes) {
                        redesEntry.detalhes.forEach((detalhe: any) => {
                          if (detalhe.value) {
                            Object.values(detalhe.value).forEach((platform: any) => {
                              if (Array.isArray(platform)) {
                                platform.forEach((profile: any) => {
                                  Object.values(profile).forEach((field: any) => {
                                    if (field && typeof field === 'object' && 'is_deleted' in field) {
                                      field.is_deleted = !hasAnyDeleted;
                                    }
                                  });
                                });
                              }
                            });
                          }
                        });
                      }
                    });
                  }
                },
                testEntryDeleted as any,
                testSectionDeleted,
                calculateDataCount
              );
            }}
          >
            <ReportsCustomLabel
              label="REDES SOCIAIS"
              colorClass="bg-white"
              labelTextClass="text-white"
            />
          </CustomGridItem>
        </CustomGridContainer>
      );

      // Add all the platform elements
      allElements.push(...platformElements);

      return allElements.length > 0 ? <>{allElements}</> : null;
    },
  };

  const renderGenericList = (entry: PossivelPessoaRelacionada, listKey: string): React.ReactElement | null => {
    const list = (entry as any)[listKey];
    if (!Array.isArray(list) || list.length === 0) return null;

    const idx = idxMap.get(entry)!;

    const filteredItems = list.filter((item: any) => {
      if (item.value && typeof item.value === 'object') {
        return Object.values(item.value).some((field: any) => includeKey(field.is_deleted));
      }
      return includeKey(item.is_deleted);
    });

    if (filteredItems.length === 0) return null;

    const displayTitle = listKey
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return (
      <CustomGridContainer cols={1} key={`${listKey}-${idx}`} className="mb-6">
        <CustomGridItem
          cols={1}
          containerClassName="w-fit pr-12"
          onToggleField={() => onToggleGenericListTitle(idx, listKey)}
        >
          <ReportsCustomLabel
            label={displayTitle.toUpperCase()}
            colorClass="bg-white"
            labelTextClass="text-white"
          />
        </CustomGridItem>

        <CustomGridContainer cols={2}>
          {filteredItems.map((item: any, renderIdx: number) => {
            const originalIdx = list.indexOf(item);

            if (item.value && typeof item.value === 'object') {
              return (
                <GridItem key={`${listKey}-${idx}-${originalIdx}`} cols={1}>
                  <CustomGridItem
                    cols={1}
                    containerClassName="w-fit pr-12"
                    onToggleField={() => onToggleGenericListItem(idx, listKey, originalIdx)}
                  >
                    <ReportsCustomLabel
                      label={`${displayTitle.toUpperCase()} ${!isTrash ? renderIdx + 1 : ""}`}
                      colorClass="bg-border"
                      icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                    />
                  </CustomGridItem>
                  <div className="pl-5">
                    {Object.entries(item.value)
                      .filter(([_, fieldValue]: any) => includeKey(fieldValue.is_deleted))
                      .map(([fieldKey, fieldValue]: any) => {
                        // Handle array values (like vinculo)
                        if (Array.isArray(fieldValue.value)) {
                          const displayValue = fieldValue.value
                            .map((arrayItem: any) => arrayItem.rotulo || arrayItem)
                            .join(", ");

                          return (
                            <CustomGridItem
                              key={`${listKey}-${originalIdx}-${fieldKey}`}
                              cols={1}
                              className="py-1"
                              onToggleField={() => onToggleGenericListField(idx, listKey, originalIdx, fieldKey)}
                            >
                              <CustomReadOnlyInputField
                                label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                value={displayValue}
                                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                tooltip={renderSourceTooltip(fieldValue.source)}
                              />
                            </CustomGridItem>
                          );
                        }

                        return (
                          <CustomGridItem
                            key={`${listKey}-${originalIdx}-${fieldKey}`}
                            cols={1}
                            className="py-1"
                            onToggleField={() => onToggleGenericListField(idx, listKey, originalIdx, fieldKey)}
                          >
                            <CustomReadOnlyInputField
                              label={translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              value={String(fieldValue.value)}
                              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                              tooltip={renderSourceTooltip(fieldValue.source)}
                            />
                          </CustomGridItem>
                        );
                      })}
                  </div>
                </GridItem>
              );
            }
            return null;
          })}
        </CustomGridContainer>
      </CustomGridContainer>
    );
  };

  const validateKeys = (keys: Array<keyof PossivelPessoaRelacionada>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: PossivelPessoaRelacionada): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof PossivelPessoaRelacionada>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Possíveis Pessoas Relacionadas] Chaves inválidas:", keys);
    }

    const orderedKeys: Array<keyof PossivelPessoaRelacionada> = [
      'nome_completo',
      'detalhes',
      'telefones',
      'emails',
      'enderecos',
      'imagens',
      'redes_sociais'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    // Get generic list keys (following participantKeys pattern from NEW_renderProcessos.strategy.tsx)
    const genericListKeys = keys.filter(key =>
      !orderedKeys.includes(key) && Array.isArray((entry as any)[key])
    );

    const elements: React.ReactElement[] = [];

    // Render known keys first
    filteredKeys.forEach((chave) => {
      const element = formatByKey[chave]?.(entry);
      if (element) elements.push(element);
    });

    // Render generic lists
    genericListKeys.forEach((listKey) => {
      const element = renderGenericList(entry, listKey as string);
      if (element) elements.push(element);
    });

    return elements;
  };

  const render = (dataArray: PossivelPessoaRelacionada[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Possíveis Pessoas Relacionadas] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return testNomeCompletoDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testTelefonesDeleted(entry) ||
          testEnderecosDeleted(entry) ||
          testImagensDeleted(entry) ||
          testRedesSociaisDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true)) ||
          (entry.telefones && entry.telefones.some((t: any) =>
            Object.values(t.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          (entry.enderecos && entry.enderecos.some((end: any) =>
            Object.values(end.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          (entry.imagens && entry.imagens.some((img: any) =>
            Object.values(img.value || {}).some((v: any) => v.is_deleted === true)
          )) ||
          (entry.redes_sociais && entry.redes_sociais.some((redesEntry: any) =>
            redesEntry.detalhes && redesEntry.detalhes.some((detalhe: any) => {
              if (detalhe.value) {
                return Object.values(detalhe.value).some((platform: any) => {
                  if (Array.isArray(platform)) {
                    return platform.some((profile: any) =>
                      Object.values(profile).some((field: any) => field.is_deleted === true)
                    );
                  }
                  return false;
                });
              }
              return detalhe.is_deleted === true;
            })
          )) ||
          // Check generic lists for deleted items
          (() => {
            const genericListKeys = Object.keys(entry).filter(key =>
              key !== 'nome_completo' &&
              key !== 'detalhes' &&
              key !== 'telefones' &&
              key !== 'emails' &&
              key !== 'enderecos' &&
              key !== 'imagens' &&
              key !== 'redes_sociais' &&
              Array.isArray((entry as any)[key])
            );

            return genericListKeys.some(key => {
              const list = (entry as any)[key] as any[];
              return list.some((item: any) => {
                if (item.value && typeof item.value === 'object') {
                  return Object.values(item.value).some((field: any) => field.is_deleted === true);
                }
                return item.is_deleted === true;
              });
            });
          })();
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`pessoa-relacionada-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      (entry: any) => {
        // Marca o campo principal como deletado/restaurado
        if (entry.nome_completo) {
          entry.nome_completo.is_deleted = targetDeletedState;
        }

        // Handle detalhes (object)
        if (entry.detalhes && typeof entry.detalhes === 'object') {
          Object.values(entry.detalhes).forEach((field: any) => {
            if (field && typeof field === 'object' && 'is_deleted' in field) {
              field.is_deleted = targetDeletedState;
            }
          });
        }

        // Handle arrays
        [entry.telefones, entry.enderecos, entry.imagens].forEach(property => {
          if (Array.isArray(property)) {
            property.forEach((item: any) => {
              if (item.value && typeof item.value === 'object') {
                Object.values(item.value).forEach((field: any) => {
                  if (field && typeof field === 'object' && 'is_deleted' in field) {
                    field.is_deleted = targetDeletedState;
                  }
                });
              }
              if (item && typeof item === 'object' && 'is_deleted' in item) {
                item.is_deleted = targetDeletedState;
              }
            });
          }
        });

        // Handle redes_sociais
        if (entry.redes_sociais) {
          entry.redes_sociais.forEach((redesEntry: any) => {
            if (redesEntry.detalhes) {
              redesEntry.detalhes.forEach((detalhe: any) => {
                if (detalhe.value) {
                  Object.values(detalhe.value).forEach((platform: any) => {
                    if (Array.isArray(platform)) {
                      platform.forEach((profile: any) => {
                        Object.values(profile).forEach((field: any) => {
                          if (field && typeof field === 'object' && 'is_deleted' in field) {
                            field.is_deleted = targetDeletedState;
                          }
                        });
                      });
                    }
                  });
                }
              });
            }
          });
        }
      },
      testEntryDeleted as any,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
