import { create } from "zustand";

interface NewReportActions {
  setSelectedReportType: (type: string) => void;
  setReportInputValue: (value: string) => void;
  clearNewReportValues: () => void;
}

interface NewReportState {
  selectedReportType: string;
  reportInputValue: string;
  actions: NewReportActions;
}

const useNewReportStore = create<NewReportState>((set) => ({
  selectedReportType: "",
  reportInputValue: "",
  actions: {
    setSelectedReportType: (type: string) => set({ selectedReportType: type, reportInputValue: "" }),
    setReportInputValue: (value: string) => set({ reportInputValue: value }),
    clearNewReportValues: () =>
      set({ selectedReportType: "", reportInputValue: "" }),
  },
}));

export const useNewReportSelectedType = () =>
  useNewReportStore((state) => state.selectedReportType);
export const useNewReportInputValue = () =>
  useNewReportStore((state) => state.reportInputValue);
export const useNewReportActions = () =>
  useNewReportStore((state) => state.actions);
