import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  fetchUserData,
  postVerifier,
  postApi<PERSON>ey,
  postUserInvite,
  getUserInvites,
  getUserLogs,
  getUserFromInvite,
  getUserInvite,
  postAnswerInvite,
  cancelSentInvite,
  editInvitedUser,
  removeUserFromOrganization,
  getInviteDetails,
} from "~/services/gateways/user.gateway";
import type {
  EncryptedData,
  UserData,
  UserInvitesFilters,
  UserLogsFilters,
} from "~/types/global";
import { useEncryption } from "./useEncryption";
import { tanstackQueryConfig } from "~/helpers/constants";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";
import { createErrorHandler } from "~/helpers/errorHandling.helper";

export const useUserCRUD = () => {
  const { encryptData } = useEncryption();
  const queryClient = useQueryClient();
  const { checkPermission } = usePermissionCheck();
  const queryKey = ["user"];
  const saltKey = ["user", "salt"];

  const userQueryUser = useQuery<UserData>({
    queryKey: queryKey,
    queryFn: fetchUserData,
    staleTime: Infinity,
    retry: false,
  });

  const addNewVerifierMutation = useMutation({
    mutationFn: async (data: string) => {
      try {
        const { data: encrypted } = await encryptData(data);
        await postVerifier(encrypted as EncryptedData);
        return true;
      } catch (error) {
        console.error("addNewVerifierMutation Error:", error);
        return false;
      }
    },
    onSuccess: (ok) => {
      if (ok) {
        invalidateUser();
        //toast.success("Senha criada com sucesso!")
      }
    },
    onError: createErrorHandler("Erro ao tentar salvar senha", "Erro ao salvar"),
  });

  const invalidateUser = async () => {
    await queryClient.invalidateQueries({
      queryKey: queryKey,
      exact: true,
    });
  };

  const invalidateSalt = async () => {
    await queryClient.invalidateQueries({
      queryKey: saltKey,
      exact: true,
    });
  };

  const invitesQuery = (filters: UserInvitesFilters = {}) =>
    useQuery({
      queryKey: ["user", "invites", filters],
      queryFn: () => getUserInvites(filters),
      ...tanstackQueryConfig,
      staleTime: Infinity,
      retry: false,
      enabled: checkPermission(Permission.GET_ORGANIZATION_INVITE)
    });

  const userLogsQuery = (filters: UserLogsFilters = {}) =>
    useQuery({
      queryKey: ["user", "logs", filters],
      queryFn: () => getUserLogs(filters),
      ...tanstackQueryConfig,
      staleTime: Infinity,
      enabled: checkPermission(Permission.GET_USER_LOGS) || checkPermission(Permission.GET_ORGANIZATION_LOGS),
    });

  const userInviteQuery = (status_invite?: string, type_invite?: string) =>
    useQuery({
      queryKey: ["user", "invite", status_invite, type_invite],
      queryFn: () => getUserInvite(status_invite, type_invite),
      ...tanstackQueryConfig,
      staleTime: Infinity,
      enabled: checkPermission(Permission.GET_USER_INVITE),
    });

  const invalidadeUserInvite = async () => {
    await queryClient.invalidateQueries({
      queryKey: ["user", "invite"],
      exact: false,
    });
  };

  const userFromInviteQuery = (invite_id: string) =>
    useQuery({
      queryKey: ["user", "fromInvite", invite_id],
      queryFn: () => getUserFromInvite(invite_id),
      ...tanstackQueryConfig,
      enabled: !!invite_id && checkPermission(Permission.GET_DATA_FROM_USER_INVITE),
      staleTime: Infinity,
      retry: false,
    });

  const inviteDetailsQuery = (invite_id: string) =>
    useQuery({
      queryKey: ["invite", "details", invite_id],
      queryFn: () => getInviteDetails(invite_id),
      ...tanstackQueryConfig,
      enabled: !!invite_id && checkPermission(Permission.GET_INVITE_DETAILS),
      staleTime: Infinity,
      retry: false,
    });

  const setApiKeyMutation = useMutation({
    mutationFn: async (apiKey: string) => {
      if (!checkPermission(Permission.ADD_API_KEY)) {
        throw new Error("Você não tem permissão para configurar API Key");
      }
      return postApiKey(apiKey);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: queryKey,
        exact: true,
      });
      toast.success("API Key configurada com sucesso!");
    },
    onError: createErrorHandler("Erro ao tentar configurar a chave da API", "Erro ao configurar chave da API"),
  });

  const createUserInviteMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!checkPermission(Permission.INVITE_USER)) {
        throw new Error("Você não tem permissão para convidar usuários");
      }
      return postUserInvite(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "invites"],
        exact: false,
      });
      toast.success("Convite enviado com sucesso!");
    },
    onError: createErrorHandler("Erro ao tentar enviar o convite", "Erro ao enviar convite"),
  });

  const answerInviteMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!checkPermission(Permission.ANSWER_INVITE)) {
        throw new Error("Você não tem permissão para responder convites");
      }
      return postAnswerInvite(data);
    },
    onSuccess: () => {
      invalidateUser(); // invalidar apenasr user para ser deslogado
    },
    onError: createErrorHandler("Erro ao tentar responder o convite", "Erro ao responder convite"),
  });

  const cancelInviteMutation = useMutation({
    mutationFn: async (invite_id: string) => {
      if (!checkPermission(Permission.CANCEL_INVITE)) {
        throw new Error("Você não tem permissão para cancelar convites");
      }
      return cancelSentInvite(invite_id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "invites"],
        exact: false,
      });
      toast.success("Convite cancelado com sucesso!");
    },
    onError: createErrorHandler("Erro ao tentar cancelar o convite", "Erro ao cancelar convite"),
  });

  const editInvitedUserMutation = useMutation({
    mutationFn: async ({ user_id, report_types, role, credits_monthly }: {
      user_id: string;
      report_types: string[];
      role: string;
      credits_monthly: number;
    }) => {
      if (!checkPermission(Permission.EDIT_USER)) {
        throw new Error("Você não tem permissão para editar usuários");
      }
      return editInvitedUser(user_id, report_types, role, credits_monthly);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "invites"],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: ["user", "fromInvite"],
        exact: false,
      });
      toast.success("Usuário editado com sucesso!");
    },
    onError: createErrorHandler("Erro ao tentar editar o usuário", "Erro ao editar usuário"),
  });

  const removeUserFromOrganizationMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!checkPermission(Permission.REMOVE_ORGANIZATION)) {
        throw new Error("Você não tem permissão para remover usuários da organização");
      }
      return removeUserFromOrganization(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "invites"],
        exact: false,
      });
      toast.success("Usuário removido da organização com sucesso!");
    },
    onError: createErrorHandler("Erro ao tentar remover o usuário da organização", "Erro ao remover usuário da organização"),
  });

  return {
    userQueryUser,
    addNewVerifierMutation,
    invitesQuery,
    userLogsQuery,
    userInviteQuery,
    userFromInviteQuery,
    inviteDetailsQuery,
    setApiKeyMutation,
    createUserInviteMutation,
    answerInviteMutation,
    cancelInviteMutation,
    editInvitedUserMutation,
    removeUserFromOrganizationMutation,
    invalidateUser,
    invalidateSalt,
    invalidadeUserInvite
  };
};
