"""
Arquivo para brincar de testes com coisas das functions
Este arquivo NÃO é incluído no deploy de functions, não usar nada aqui pra isso!!!
"""

import time
import os
from dotenv import load_dotenv
load_dotenv('.env')
_API_KEY = os.environ['SNAP_API_KEY']
_CAPTCHA_KEY = os.environ['CAPTCHA_KEY']


class MockRequest:

    class Auth:

        def __init__(self, uid):
            self.uid = uid

    class RawRequest:

        def __init__(self, token):
            self.headers = {'Authorization': f'Bearer {token}'}

    def __init__(self, uid, data, token):
        self.auth = self.Auth(uid)
        self.data = data
        self.raw_request = self.RawRequest(token)


# if __name__ == '__main__':

    # my_report = CPFReport(report_id='', cpf='01629906697')
    # start = time.perf_counter()
    # res = my_report.make_report(api_key=_API_KEY, captcha_key=_CAPTCHA_KEY)
    # end = time.perf_counter()
    # elapsed = end - start
    # print(f'Tempo de execução: {elapsed} s = {int(elapsed/60)} min {elapsed % 60} s')
    # # #print(json.dumps(res, indent=4, ensure_ascii=False))
    # #
    # # end = time.time()
    # # elapsed = end - start
    # # print(f'Tempo de geração do relatório: {elapsed} s')
    # # print(f'Bases consultadas: {", ".join(list(res.keys()))}')
    # breakpoint()
