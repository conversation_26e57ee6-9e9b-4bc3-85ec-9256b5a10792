import React from 'react';
import ReportDetailComponent from '../../../components/ReportDetailComponent';
import ReportProfileHeader from '../../../components/ReportProfileHeader';
import { ReportSection, ReportMetadata } from '../../../../global';
import { ReportProvider, RenderMode } from '../../../../context/ReportContext';
import PrintButton from '../../../components/PrintButton';
import { Draft } from 'immer';
import { TooltipProvider } from "../../../components/base/tooltip";
import { ModalInstance } from "@snap/design-system"

export interface ReportDetailStore {
  sections: ReportSection[];
  metadata: ReportMetadata; //TODO - nome do report pode ser alterado pelo usuário
  reportType: string;
  isTrashEnabled: boolean,
  isPrintEnabled: boolean,
  image?: string | undefined;
  actions: {
    setMetadata: (metadata: ReportMetadata) => void;
    setReportSections: (sections: ReportSection[]) => void;
    setReportType: (type: string) => void;
    setProfileImage?: (image: string) => void;
    updateSectionEntries?: (
      sectionTitle: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>, index?: number) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean,
      calculateDataCountFn?: (section: ReportSection) => number,
      includeSubsections?: boolean,
      crossSectionUpdate?: { matchingProp: string; updaterFn: (entry: any, index?: number) => void }
    ) => void;
    updateSubsectionWithMainSection?: (
      sectionTitle: string,
      subsectionName: string,
      matchingProp: string,
      updaterFn: (entry: Draft<ReportSection["data"][0]>, index?: number) => void,
      testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
      testSectionDeletedFn: (section: ReportSection) => boolean,
      calculateDataCountFn?: (section: ReportSection) => number
    ) => void;
  };
}

export interface PersonDetailsPageProps {
  store: ReportDetailStore;
  renderMode?: RenderMode;
}

export const __PersonDetailsPage: React.FC<PersonDetailsPageProps> = ({ store, renderMode = 'default' }) => {
  const metadata = store?.metadata || ({} as ReportMetadata)
  const sections = store?.sections || [];
  const reportType = store?.reportType || '';
  const isTrashEnabled = store?.isTrashEnabled || false;
  const isPrintEnabled = store?.isPrintEnabled || false;
  const profileImage = store?.image;

  const renderContent = () => {
    if (!metadata || !sections) {
      console.error('Metadata or sections are not defined');
      return <div>Sem dados para exibir</div>;
    }

    return (
      <div className="relative">
        {
          isPrintEnabled && sections.length > 0 ?
            <PrintButton /> // botão de print
            : null
        }
        {
          renderMode === 'default' && (
            <div className="pb-4">
              <ReportProfileHeader />
            </div>
          )}
        <ReportDetailComponent />
      </div>
    )
  };

  return (
    <ReportProvider
      sections={sections}
      metadata={metadata}
      reportType={reportType}
      isTrashEnabled={isTrashEnabled}
      isPrintEnabled={isPrintEnabled}
      image={profileImage}
      renderMode={renderMode}
      actions={store.actions}
    >
      <TooltipProvider>
        <ModalInstance />
        {renderContent()}
      </TooltipProvider>
    </ReportProvider >
  );
};