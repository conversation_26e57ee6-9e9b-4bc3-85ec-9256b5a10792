from enum import Enum
from core.constants import Status, InviteStatusCons, InviteTypeCons

class UserStatus(str, Enum):
    ATIVO = Status.ativo
    INATIVO = Status.inativo


class OrganizationStatus(str, Enum):
    ATIVO = Status.ativo
    INATIVO = Status.inativo


class InviteStatus(str, Enum):
    ENVIADO = InviteStatusCons.enviado
    NEGADO = InviteStatusCons.negado
    ACEITO = InviteStatusCons.aceito
    CANCELADO = InviteStatusCons.cancelado


class InviteType(str, Enum):
    INVESTIGADOR = InviteTypeCons.investigador
    ADMINISTRADOR = InviteTypeCons.administrador
