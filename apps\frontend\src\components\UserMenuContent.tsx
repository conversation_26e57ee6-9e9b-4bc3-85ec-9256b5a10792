import { ChamferBox, List, ListItem, Menu, MenuItem, Separator, Text, useModalControl } from '@snap/design-system'
import { FiTool } from 'react-icons/fi'
import { formatIsoDate, isEmptyObject } from '~/helpers';
import { USER_CONSTANTS } from '~/helpers/constants';
import { useUserData } from '~/store/userStore'
import { ReportCredits } from '~/types/global';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { Info } from 'lucide-react';
import { ConsultasInfoDialog } from './ConsultasInfoDialog';

interface UserMenuContentProps {
  menuActions?: MenuItem[] | null;
  showRemainingCredits?: boolean;
  classNameContainer?: string;
}

const UserMenuContent = ({ menuActions = null, showRemainingCredits = false, classNameContainer = "" }: UserMenuContentProps) => {
  const userData = useUserData();
  const { open } = useModalControl();
  const userProfile = userData?.[USER_CONSTANTS.user_data.role as keyof typeof userData] as string;
  const creditsValidateUntil = userData?.[USER_CONSTANTS.user_data.next_reset_credits as keyof typeof userData] as string;
  const userReportTypes = userData?.[USER_CONSTANTS.user_data.report_count as keyof typeof userData] as ReportCredits;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;
  const isUserStandalone = userProfile === USER_CONSTANTS.profile_types.standalone;

  const getConsultasRealizadas = () => {
    const userReportsCount = userData?.[USER_CONSTANTS.user_data.report_count as keyof typeof userData] || {};
    const reportSum = Object.values(userReportsCount || {}).reduce((acc, cur) => acc + cur, 0);
    return reportSum;
  };

  const renderConsultasInfo = () => {
    return (
      <Tooltip delayDuration={0}>
        <TooltipTrigger className="cursor-help" asChild>
          <Info size={14} className="text-accent" />
        </TooltipTrigger>
        <TooltipContent side="bottom" className="!p-0 !bg-transparent !shadow-none !border-none max-w-[288px]">
          <ChamferBox corner="topLeft" className="rounded-md px-0.5 pt-0.5 bg-neutral-800">
            <div className="relative z-10">
              <Text className="text-left text-netral-100">
                Você possui <span className="font-bold"><span className="text-accent">{`${!isUserStandalone ? "*" : ""}`}</span>{` ${userCredits} consultas `}</span>
              </Text>
              <Text className="text-left text-netral-100">
                válidas até a data <span className="font-semibold">{formatIsoDate(creditsValidateUntil, true)}</span>.
              </Text>
              {
                !isUserStandalone && (
                  <div>
                    <Separator className="my-2 border-border" />
                    <Text className="text-left text-neutral-200">
                      <span className="font-bold text-accent">*</span> {`Saiba mais sobre as consultas disponíveis `}
                      <span className="text-accent underline cursor-pointer" onClick={handleOpenConsultasInfo} title="Abrir mais informações de consultas">
                        clicando aqui.
                      </span>
                    </Text>

                  </div>
                )
              }
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    );
  };

  const handleOpenConsultasInfo = () => {
    open({
      modal: () => ({
        title: "INFORMAÇÕES SOBRE CONSULTAS",
        content: <ConsultasInfoDialog.Content />,
        footer: <ConsultasInfoDialog.Footer />,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    });
  };

  return (
    <div>
      {/* tag tipo perfil */}
      <div className="flex items-center gap-2 bg-[#2F3240] p-3 mb-1">
        <FiTool size={16} />
        <Text variant="body-md" className="font-semibold capitalize">{userProfile || "Não Informado"}</Text>
      </div>

      <div className={`flex flex-col gap-4 p-4 ${classNameContainer}`}>
        {/* consultas disponíveis */}
        {
          showRemainingCredits ? (
            <div className="flex items-center gap-2 justify-between">
              <div className="flex flex-col items-start gap-0.5">
                <div className="flex items-center gap-2">
                  <Text variant="body-lg" className="font-semibold">Consultas disponíveis</Text>
                  {renderConsultasInfo()}
                </div>
                {creditsValidateUntil ? (
                  <Text variant="body-sm" className="opacity-80">{`Válidas até ${formatIsoDate(creditsValidateUntil, true)}`}</Text>
                ) : null}
              </div>
              <Text variant="title-lg" className="font-semibold">{userCredits || 0}</Text>
            </div>
          ) : null
        }
        <List className="flex flex-col gap-4">
          {/* consultas realizadas */}
          <ListItem className="flex items-center gap-2 bg-accent py-1.5 px-2 justify-between rounded-xs">
            <Text variant="body-md">Consultas realizadas:</Text>
            <Text variant="body-md" className="font-semibold">{getConsultasRealizadas()}</Text>
          </ListItem>
          {/* tipos de relatório */}
          <div className="flex flex-col gap-0.5">
            {userReportTypes &&
              !isEmptyObject(userReportTypes) ? (
              Object.entries(userReportTypes)?.map(([key, value], index) => (
                <ListItem
                  key={index}
                  className="flex items-center gap-2 bg-card py-1.5 px-2 justify-between rounded-xs uppercase"
                >
                  <Text >{key}</Text>
                  <Text variant="body-md" className="font-semibold">{value}</Text>
                </ListItem>
              ))
            ) : (
              <ListItem className="opacity-80">
                Nenhum tipo de relatório permitido.
              </ListItem>
            )}
          </div>
        </List>
        {menuActions ?
          <Menu
            items={menuActions}
            separator
            gap="none"
          /> : null
        }
      </div>

    </div>
  )
}

export default UserMenuContent